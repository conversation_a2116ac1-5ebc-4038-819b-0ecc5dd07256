import 'package:get/get.dart';

import '../../../../controllers/upholdSqflite_controller.dart';

class UpholdRecordController extends GetxController {
  //TODO: Implement UpholdRecordController
  UpholdSQLController upholdSQLController = Get.find();

  RxString titleName = "维护记录".obs;
  RxList historyList = [].obs;

  @override
  void onInit() {
    super.onInit();
    getHistory();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 获取历史记录
  getHistory()async{
    var historyResult = await upholdSQLController.findHistory("");
    historyList.value = historyResult;
  }

}
