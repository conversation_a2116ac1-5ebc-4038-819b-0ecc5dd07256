import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/uphold/startUphold/controllers/uphold_start_uphold_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../../../../controllers/global_controller.dart';
import '../controllers/uphold_worl_order_controller.dart';

class UpholdWorlOrderView extends GetView<UpholdWorlOrderController> {
  GlobalController globalController = Get.find<GlobalController>();
  UpholdWorlOrderView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: PreferredSize(
            preferredSize: Size.fromHeight(MyScreenUtil.height(60)),
            child: AppBar(
              title: Obx(() => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('${controller.titleName}'),
                      Container(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [],
                        ),
                      )
                    ],
                  )),
              centerTitle: true,
            )),
        body: Obx(() => Column(
              children: [
                Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: MyScreenUtil.width(20)),
                      decoration: BoxDecoration(color: Colors.white),
                      width: MyScreenUtil.width(250),
                      // height: MyScreenUtil.width(505),
                      child: Obx(
                        () => DropdownButtonFormField(
                          value: controller.selectedOption.value,
                          onChanged: (value) {
                            // 处理选中项的逻辑
                            controller.selectedOption.value = value as String;
                            controller.findWork();
                          },
                          items: controller.deviceList
                              .map((option) => DropdownMenuItem(
                                    value: option['id'],
                                    child: Text(option['name']),
                                  ))
                              .toList(),
                          decoration: InputDecoration(
                            labelText: '根据专业筛选',
                          ),
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: MyScreenUtil.width(20)),
                      decoration: BoxDecoration(color: Colors.white),
                      width: MyScreenUtil.width(250),
                      // height: MyScreenUtil.width(505),
                      child: Obx(
                        () => DropdownButtonFormField(
                          value: controller.selectedNameOption.value,
                          onChanged: (value) {
                            // 处理选中项的逻辑
                            controller.selectedNameOption.value = value as String;
                            controller.findWork();
                          },
                          items: controller.deviceNameList
                              .map((option) => DropdownMenuItem(
                                    value: option,
                                    child: Text(option),
                                  ))
                              .toList(),
                          decoration: InputDecoration(
                            labelText: '根据设备名称筛选',
                          ),
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.all(MyScreenUtil.width(10)),
                      width: MyScreenUtil.width(250),
                      child: TextField(
                          controller: controller.textController,
                          style: TextStyle(
                              fontSize: MyScreenUtil.fontSize(18),
                              fontWeight: FontWeight.w500),
                          decoration: InputDecoration(
                              hintText: "请输入设备编号",
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.only(
                                top: MyScreenUtil.height(10),
                                bottom: MyScreenUtil.height(10),
                                left: MyScreenUtil.height(10),
                                right: MyScreenUtil.height(10),
                              )),
                          onChanged: (value) {
                            controller.searchValue.value = value;
                            controller.findWork();
                          }),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        controller.searchValue.value = '';
                        controller.selectedOption.value = '1';
                        controller.textController.clear();
                        controller.findWork();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: MyScreenUtil.ThemColor(), // 背景颜色
                        disabledBackgroundColor:
                            const Color.fromRGBO(143, 147, 153, 1),
                      ),
                      child: Text(
                        "重置",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: MyScreenUtil.fontSize(20)),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: ListView(
                    children: controller.workList
                        .map((item) {
                          int index = controller.workList.indexOf(item);
                          return worlList(item);
                        })
                        .toList()
                        .cast<Widget>(),
                  ),
                ),
              ],
            )));
  }

  // 工单列表
  worlList(workItem) {
    // final user =  controller.handelRole(workItem['id']);
    // print(user);
    if (workItem['itemShow'] == 1) {
      return Container(
        margin: EdgeInsets.all(MyScreenUtil.width(10)),
        decoration: BoxDecoration(
            border: Border.all(
                color: workItem["isFinish"] != 0
                    ? const Color.fromRGBO(238, 238, 238, 1)
                    : MyScreenUtil.ThemColor(),
                width: 1),
            borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))),
        child: Column(
          children: [
            // 标题
            Container(
              decoration: BoxDecoration(
                  color: workItem["isFinish"] != 0
                      ? const Color.fromRGBO(238, 238, 238, 1)
                      : const Color.fromRGBO(249, 230, 232, 1),
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(MyScreenUtil.radius(10)),
                      topRight: Radius.circular(MyScreenUtil.radius(10)))),
              padding: EdgeInsets.all(10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("执行工单",
                      style: TextStyle(
                          color: const Color.fromRGBO(43, 51, 63, 1),
                          fontSize: MyScreenUtil.fontSize(18))),
                  Row(
                    children: [
                      Text(workItem["isFinish"] != 0 ? "已完成" : "待维护",
                          style: TextStyle(
                              color: workItem["isFinish"] != 0
                                  ? const Color.fromRGBO(43, 51, 63, 1)
                                  : const Color.fromRGBO(195, 12, 34, 1),
                              fontSize: MyScreenUtil.fontSize(18))),
                      workItem["isFinish"] != 0
                          ? Container(
                              margin:
                                  EdgeInsets.only(left: MyScreenUtil.width(16)),
                              child: Text(
                                  "完成时间：${controller.formatTime('${workItem['finishDate']}')}"),
                            )
                          : const Text("")
                    ],
                  )
                ],
              ),
            ),
            // 内容
            Container(
              margin: EdgeInsets.only(
                  top: MyScreenUtil.height(20),
                  bottom: MyScreenUtil.height(20)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            "维护工单编号: ${workItem['maintenancePlanNum']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          width:MyScreenUtil.width(380),
                          child: Text(
                            "设备位置: ${workItem['installationSite']}",
                            overflow: TextOverflow.ellipsis,
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            "执行人: ${workItem['operator']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        ),
                        Container(
                          child: Text(
                            "厂家: ${workItem['factory'] ?? ''}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        )
                      ],
                    ),
                  ),
                  Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            "设备名称: ${workItem['deviceGroupName']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            "设备编号: ${workItem['deviceNum']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            "监督员: ${workItem['supervision']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            "维护类型: ${workItem['typeName']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: workItem['week'] != null
                              ? Text(
                                  "维护时间: ${workItem['typeName']}第${workItem['week']}周",
                                  style: TextStyle(
                                      fontSize: MyScreenUtil.fontSize(20)),
                                )
                              : Text(
                                  "维护时间: ${workItem['typeName']}",
                                  style: TextStyle(
                                      fontSize: MyScreenUtil.fontSize(20)),
                                ),
                        ),
                        Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          child: Text(
                            "专业工程师: ${workItem['pe']}",
                            style:
                                TextStyle(fontSize: MyScreenUtil.fontSize(20)),
                          ),
                        )
                      ],
                    ),
                  ),
                  workItem["isFinish"] == 0
                      ? Container(
                          child: Column(
                          children: [
                            ElevatedButton(
                              onPressed: () {
                                controller.previewWorl(workItem);
                              },
                              style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      Color.fromARGB(255, 0, 125, 197)),
                              child: Text(
                                "预览工单",
                                style: TextStyle(
                                    fontSize: MyScreenUtil.fontSize(20)),
                              ),
                            ),
                            ElevatedButton(
                              onPressed:
                                  // workItem['isPreview'] != 0 ?
                                  () {
                                    Get.delete<UpholdStartUpholdController>();
                                
                                Get.toNamed("/start-uphold", arguments: {
                                  "workItem": workItem,
                                  "id": workItem["id"],
                                  "deptName": workItem['deptName'], // 部门
                                  "deviceGroupName":
                                      workItem['deviceGroupName'], // 设备类型
                                  "typeName": workItem['typeName'], // 维护类型
                                  "installationSite":
                                      workItem['installationSite'] // 设备位置
                                });
                                Get.put(UpholdStartUpholdController());
                              },
                              // : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor:
                                    MyScreenUtil.ThemColor(), // 背景颜色
                                disabledBackgroundColor:
                                    const Color.fromRGBO(143, 147, 153, 1),
                              ),
                              child: Text(
                                "开始维护",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: MyScreenUtil.fontSize(20)),
                              ),
                            )
                          ],
                        ))
                      : Container(
                          child: ElevatedButton(
                              onPressed: () {
                                controller.workSubmit(workItem);
                              },
                              child: Text("上传数据"),
                              style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      const Color.fromARGB(255, 0, 125, 197))),
                        )
                ],
              ),
            )
          ],
        ),
      );
    } else {
      return Container();
    }
  }
}
