import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../utils/screenutil.dart';
import '../controllers/uphold_start_examine_controller.dart';

class UpholdStartExamineView extends GetView<UpholdStartExamineController> {
  const UpholdStartExamineView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
      return Obx(()=>Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(MyScreenUtil.height(60)),
          child: AppBar(
            title: Row(
              mainAxisAlignment:MainAxisAlignment.start,
              children: [
                Text(controller.titleName.value)
              ],
            ),
            centerTitle: true,
          ),
        ),
        body: Container(
          margin: const EdgeInsets.all(20),
          decoration: const BoxDecoration(
            // color: Colors.red
          ),
          child: Row(
            children: [
              Container(
                width: MyScreenUtil.width(180),
                padding: EdgeInsets.only(left: MyScreenUtil.width(20),right:MyScreenUtil.width(20),top:MyScreenUtil.width(20) ),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Color.fromRGBO(230, 230, 230, 1),
                    width: 1
                  ),
                  borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))
                ),
                child: ListView(
                  children: catalogue(),
                ),
              ),

              Expanded(
                flex: 1,
                child: Container(
                  decoration: const BoxDecoration(),
                  padding: const EdgeInsets.only(left: 10),
                  child: ListView(
                    children: [
                      controller.componentArr[controller.componentIndex.value]
                    ],
                  ),
                )
              )
              
            ],
          ),
        ),
      ));
  }

  // 左侧目录
  catalogue(){
    return  [
      Container(
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
              child: Icon(
                Icons.radio_button_checked,
                size: MyScreenUtil.fontSize(18),
                color: controller.componentIndex.value >=0 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(bottom: MyScreenUtil.height(20),top: MyScreenUtil.height(20)),
              child: Text("设备信息",
                style: TextStyle(
                  color: controller.componentIndex.value >=0 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: MyScreenUtil.fontSize(16)
                )
              ),
            ), 
          ],
        ),
      ),
      Container(
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
              child: Icon(
                Icons.radio_button_checked,
                size: MyScreenUtil.fontSize(18),
                color: controller.componentIndex.value >=1 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(bottom: MyScreenUtil.height(20),top: MyScreenUtil.height(20)),
              child: Text("先提条件",
                style: TextStyle(
                  color: controller.componentIndex.value >=1 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: MyScreenUtil.fontSize(16)
                )
              ),
            ), 
          ],
        ),
      ),
      Container(
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
              child: Icon(
                Icons.radio_button_checked,
                size: MyScreenUtil.fontSize(18),
                color: controller.componentIndex.value >=2 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(bottom: MyScreenUtil.height(20),top: MyScreenUtil.height(20)),
              child: Text("安全保障",
                style: TextStyle(
                  color: controller.componentIndex.value >=2 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: MyScreenUtil.fontSize(16)
                )
              ),
            ), 
          ],
        ),
      ),
      Container(
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
              child: Icon(
                Icons.radio_button_checked,
                size: MyScreenUtil.fontSize(18),
                color: controller.componentIndex.value >=3 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(bottom: MyScreenUtil.height(20),top: MyScreenUtil.height(20)),
              child: Text("工具及备件要求",
                style: TextStyle(
                  color: controller.componentIndex.value >=3 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: MyScreenUtil.fontSize(16)
                )
              ),
            ), 
          ],
        ),
      ),
      Container(
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
              child: Icon(
                Icons.radio_button_checked,
                size: MyScreenUtil.fontSize(18),
                color: controller.componentIndex.value >=4 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(bottom: MyScreenUtil.height(20),top: MyScreenUtil.height(20)),
              child: Text("回退计划",
                style: TextStyle(
                  color: controller.componentIndex.value >=4 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: MyScreenUtil.fontSize(16)
                )
              ),
            ), 
          ],
        ),
      ),
      Container(
        child: Row(
          children: [
            Container(
              margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
              child: Icon(
                Icons.radio_button_checked,
                size: MyScreenUtil.fontSize(18),
                color: controller.componentIndex.value >=5 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(bottom: MyScreenUtil.height(20),top: MyScreenUtil.height(20)),
              child: Text("操作流程",
                style: TextStyle(
                  color: controller.componentIndex.value >=5 ?MyScreenUtil.ThemColor() : Color.fromRGBO(117, 122, 136, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: MyScreenUtil.fontSize(16)
                )
              ),
            ), 
          ],
        ),
      ),


    ];
  }


}
