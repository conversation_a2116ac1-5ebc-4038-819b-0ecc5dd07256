import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../utils/screenutil.dart';
import '../controllers/uphold_start_examine_controller.dart';

// 安全保障
class Ensure extends GetView<UpholdStartExamineController> {
  const Ensure({Key? key}) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Container(
            child: Column(
              children:controller.guaranteeData["details"].map((guaranteeItem){
                return rowSectionFun(guaranteeItem,"维护完成");
              }).toList().cast<Widget>()
            ),
          ),
          
          // 签字
          // Container(
          //   decoration: BoxDecoration(
          //     color: Color.fromRGBO(44, 185, 129, 0.259),
          //     border: Border.all(
          //       color: Color.fromRGBO(44, 185, 129, 0.259),
          //       width: 1
          //     ),
          //     borderRadius: BorderRadius.only(
          //       topLeft: Radius.circular(MyScreenUtil.radius(10)),
          //       topRight: Radius.circular(MyScreenUtil.radius(10)),
          //     )
          //   ),
          //   padding: EdgeInsets.all(MyScreenUtil.height(10)),
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       Container(
          //         child:const Text(
          //           "签字",
          //           style:TextStyle(
          //             color: Color.fromRGBO(43, 51, 63, 1)
          //           ),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),

          // 签字回显
          Container(
            child: Column(
              children: controller.signDataList.map((item){
                return signListFun(item);
              }).toList().cast<Widget>()
            ),
          ),

          Row(
            children: [
              Expanded(
                flex: 1,
                child: Container(
                  margin: EdgeInsets.only(top: MyScreenUtil.width(40),bottom:MyScreenUtil.width(40) ),
                  child: ElevatedButton(
                    onPressed: (){
                      controller.handelComponentIndex(3);
                    },
                    child: const Text('下一项'),
                  ),
                )
              )
            ],
          )



        ],
      ),
    );
  }

  // 数据项方法
  rowSectionFun(guaranteeItem,state){
    return Container(
      margin: EdgeInsets.only(bottom: MyScreenUtil.width(10)),
      decoration: BoxDecoration(
        border: Border.all(
          color: Color.fromRGBO(44, 185, 129, 0.259),
          width: 1
        ),
        borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))
      ),
      child:Column(
        children: [
          // 标题
          Container(
            decoration: BoxDecoration(
              color: Color.fromRGBO(44, 185, 129, 0.259),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(MyScreenUtil.radius(10)),
                topRight: Radius.circular(MyScreenUtil.radius(10)),
                
              )
            ),
            padding: EdgeInsets.all(MyScreenUtil.height(10)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 1,
                  child: Container(
                    child:Text(
                      guaranteeItem['content'],
                      style:const TextStyle(
                        color: Color.fromRGBO(43, 51, 63, 1)
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(right: MyScreenUtil.width(20)),
                  child: Text(
                    state,
                    style:const TextStyle(
                      color: Color.fromRGBO(29, 180, 119, 1)
                    ),
                  ),
                )
              ],
            ),
          ),

          // 输入项渲染
          guaranteeItem['fillingValue']==1?Container(
            padding: EdgeInsets.only(left: MyScreenUtil.width(14),right: MyScreenUtil.width(14)),
            height:MyScreenUtil.height(50),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: createInputComponent(guaranteeItem).cast<Widget>(),
            ),
          ):Container(),

          //回显图片
            guaranteeItem['gapPicture'].length != 0
                ? Container(
                    padding: EdgeInsets.only(
                        top: MyScreenUtil.width(20),
                        left: MyScreenUtil.width(14),
                        right: MyScreenUtil.width(14)),
                    child: Row(
                      children: [
                        Container(
                            width: MyScreenUtil.width(100),
                            height: MyScreenUtil.height(100),
                            margin:
                                EdgeInsets.only(right: MyScreenUtil.width(20)),
                            child: Image.network(
                              guaranteeItem['gapPicture'][0],
                              fit: BoxFit.cover, // 调整图像大小以适应容器
                            ))
                      ],
                    ),
                  )
                : Container(),


          // 表格
          Container(
            padding: EdgeInsets.only(top: MyScreenUtil.width(20),bottom:MyScreenUtil.width(20) ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Container(
                  width: MyScreenUtil.width(200),
                  child: Text(
                    "执行人：${controller.parseOperator(controller.workData['operator'])}",
                    style: TextStyle(fontSize: MyScreenUtil.fontSize(16)),
                  ),
                ),
                // Container(
                //   alignment: Alignment.centerLeft,
                //   child: Column(
                //     children: [
                //       Container(
                //         // width: MyScreenUtil.width(200),
                //         child:Row(
                //           children: [
                //             Container(child: Text("执行结果",style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),)),
                //             Container(child: Icon(
                //               Icons.expand_more,
                //               color: Color.fromRGBO(29, 180, 119, 1),
                //             ))
                //           ],
                //         ), 
                //       ),
                //       Container(
                //         // width: MyScreenUtil.width(200),
                //         child: Row(
                //           mainAxisAlignment: MainAxisAlignment.start,
                //           children: [
                //             Container(child: Text("执行时间",style: TextStyle(fontSize: MyScreenUtil.fontSize(16)),)),
                //             Container(child: Text("${controller.formatTime('${guaranteeItem['operatorTime']}')}",style: TextStyle(fontSize: MyScreenUtil.fontSize(16)),))
                //           ],
                //         )
                //       ),
 
                //     ],
                //   ),
                // ),
                Container(
                  child: Text("监督人：${controller.workData['supervision']['name']}",style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),),
                ),
                Container(
                  alignment: Alignment.centerLeft,
                  child: Column(
                    children: [
                      Container(
                        // width: MyScreenUtil.width(200),
                        child:Row(
                          children: [
                            Container(child: Text("检查结果",style: TextStyle(fontSize: MyScreenUtil.fontSize(18)))),
                            Container(child: Icon(
                              Icons.expand_more,
                              color: Color.fromRGBO(29, 180, 119, 1),
                            ))
                          ],
                        ), 
                      ),
                      Container(
                        // width: MyScreenUtil.width(200),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(child: Text("检查时间",style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),)),
                            Container(child: Text("${controller.formatTime('${guaranteeItem['supervisorTime']}')}",style: TextStyle(fontSize: MyScreenUtil.fontSize(16))))
                          ],
                        )
                      ),

                    ],
                  ),
                ),
              ],
            ),
          )
        ],
      )
    );
  }

  // 签字方法
  signListFun(signItem){
    return Container(
      child: Column(
        children: [
          Container(
            decoration:BoxDecoration(
              border: Border.all(
                color: Color.fromRGBO(44, 185, 129, 0.259),
                width: 1
              ),
              borderRadius: BorderRadius.circular(10)
            ),
            padding: EdgeInsets.all(MyScreenUtil.width(20)),
            margin: EdgeInsets.only(top: MyScreenUtil.width(10)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  child: Text("${signItem['role']}签字: ${signItem['name']}"),
                ),
                Container(
                  width: MyScreenUtil.width(300),
                  height: MyScreenUtil.height(100),
                  child: Image.network(signItem['imageUrl'],fit:BoxFit.cover),
                ),
                Container(
                  child: Text("签字日期: ${controller.formatTime('${signItem['date']}')}"),
                ),
              ],
            ),
          )

        ],
      ),
    );
  }

  // 输入值回显
  createInputComponent(guaranteeItem){
    var inputComponent = [];
    for(int i=0;i<guaranteeItem["amount"];i++){
      inputComponent.add(
        Container(
          margin: EdgeInsets.only(right: MyScreenUtil.height(14)),
          child: Text(
            "数值: ${guaranteeItem['gapPrice'][i]}",
            style: TextStyle(
              fontSize: MyScreenUtil.fontSize(18)
            ),
          ),
        )
      );
    }
    return inputComponent;
  }


}
