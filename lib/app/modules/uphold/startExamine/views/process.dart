import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../controllers/global_controller.dart';
import '../../../../utils/screenutil.dart';
import '../controllers/uphold_start_examine_controller.dart';

// 操作流程
class Process extends GetView<UpholdStartExamineController> {
  GlobalController globalController = Get.find();

  Process({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      children: [
        Container(
          child: Column(
              children: controller.flowData['items']
                  .map((flowItem) {
                    return maintainEntriesParent(flowItem);
                  })
                  .toList()
                  .cast<Widget>()),
        ),

        Container(
          decoration: BoxDecoration(
              color: Color.fromRGBO(44, 185, 129, 0.259),
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(MyScreenUtil.radius(10)),
                  topRight: Radius.circular(MyScreenUtil.radius(10)))),
          padding: EdgeInsets.all(MyScreenUtil.height(10)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: const Text(
                  "签字",
                  style: TextStyle(color: Color.fromRGBO(43, 51, 63, 1)),
                ),
              ),
            ],
          ),
        ),

        // 签字回显
        Container(
          child: Column(
              children: controller.signDataList
                  .map((item) {
                    return signListFun(item);
                  })
                  .toList()
                  .cast<Widget>()),
        ),

        Container(
          margin: EdgeInsets.only(top: MyScreenUtil.height(20)),
          decoration: BoxDecoration(
              color: Color.fromRGBO(249, 230, 232, 1),
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(MyScreenUtil.radius(10)),
                  topRight: Radius.circular(MyScreenUtil.radius(10)))),
          padding: EdgeInsets.all(MyScreenUtil.height(10)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: const Text(
                  "审核人签字",
                  style: TextStyle(color: Color.fromRGBO(43, 51, 63, 1)),
                ),
              ),
            ],
          ),
        ),
        executorSign("审核人", controller.workData['pe']),

        // 完成审核
        Row(
          children: [
            Expanded(
                flex: 1,
                child: Container(
                  height: MyScreenUtil.height(52),
                  margin: EdgeInsets.only(
                      top: MyScreenUtil.height(20),
                      bottom: MyScreenUtil.height(20)),
                  child: ElevatedButton(
                    onPressed: () {
                      controller.uploadExamine();
                    },
                    child: const Text("完成审核"),
                  ),
                ))
          ],
        )
      ],
    ));
  }

  // 步骤抽离
  maintainEntriesParent(flowItem) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(10),
              margin: EdgeInsets.only(bottom: MyScreenUtil.height(10)),
              decoration: BoxDecoration(
                  color: const Color.fromRGBO(238, 238, 238, 1),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(MyScreenUtil.radius(10)),
                    topRight: Radius.circular(MyScreenUtil.radius(10)),
                  )),
              child: Text(flowItem['name'],
                  style: TextStyle(
                      color: Color.fromRGBO(43, 51, 63, 1),
                      fontSize: MyScreenUtil.fontSize(16))),
            ))
          ],
        ),
        Container(
          child: Column(
            children: flowItem['details']
                .map((detailsItem) {
                  return maintainEntries(detailsItem, "维护成功");
                })
                .toList()
                .cast<Widget>(),
            // children: [
            //   maintainEntries("确认设备编号，明确维护对象","维护成功"),
            // ],
          ),
        )
      ],
    );
  }

  // 步骤条目
  maintainEntries(detailsItem, state) {
    return Container(
        margin: EdgeInsets.only(bottom: MyScreenUtil.width(10)),
        decoration: BoxDecoration(
            border: Border.all(
                color: Color.fromRGBO(44, 185, 129, 0.259), width: 1),
            borderRadius: BorderRadius.circular(MyScreenUtil.radius(10))),
        child: Column(
          children: [
            // 标题
            Container(
              decoration: BoxDecoration(
                  color: Color.fromRGBO(44, 185, 129, 0.259),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(MyScreenUtil.radius(10)),
                    topRight: Radius.circular(MyScreenUtil.radius(10)),
                  )),
              padding: EdgeInsets.all(MyScreenUtil.height(10)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                      flex: 1,
                      child: Container(
                        child: Text(
                          detailsItem['content'],
                          style: TextStyle(
                              color: Color.fromRGBO(43, 51, 63, 1),
                              fontSize: MyScreenUtil.fontSize(16)),
                        ),
                      )),
                  Container(
                    child: Text(
                      state,
                      style: TextStyle(
                          color: Color.fromRGBO(29, 180, 119, 1),
                          fontSize: MyScreenUtil.fontSize(16)),
                    ),
                  )
                ],
              ),
            ),

            // 输入项渲染
            detailsItem['fillingValue'] == 1
                ? Container(
                    padding: EdgeInsets.only(
                        left: MyScreenUtil.width(14),
                        right: MyScreenUtil.width(14)),
                    height: MyScreenUtil.height(50),
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children:
                          createInputComponent(detailsItem).cast<Widget>(),
                    ),
                  )
                : Container(),

            //回显图片
            detailsItem['gapPicture'].length != 0
                ? Container(
                    padding: EdgeInsets.only(
                        top: MyScreenUtil.width(20),
                        left: MyScreenUtil.width(14),
                        right: MyScreenUtil.width(14)),
                    child: Row(
                      children: [
                        Container(
                            width: MyScreenUtil.width(100),
                            height: MyScreenUtil.height(100),
                            margin:
                                EdgeInsets.only(right: MyScreenUtil.width(20)),
                            child: Image.network(
                              detailsItem['gapPicture'][0],
                              fit: BoxFit.cover, // 调整图像大小以适应容器
                            ))
                      ],
                    ),
                  )
                : Container(),

            // 表格
            Container(
              padding: EdgeInsets.only(
                  top: MyScreenUtil.width(20), bottom: MyScreenUtil.width(20)),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Container(
                    width: MyScreenUtil.width(200),
                    child: Text(
                        "执行人：${controller.parseOperator(controller.workData['operator'])}",
                        style: TextStyle(fontSize: MyScreenUtil.fontSize(16))),
                  ),
                  // Container(
                  //   alignment: Alignment.centerLeft,
                  //   child: Column(
                  //     children: [
                  //       Container(
                  //         // width: MyScreenUtil.width(200),
                  //         child:Row(
                  //           children: [
                  //             Container(child: Text("执行结果",style: TextStyle(fontSize: MyScreenUtil.fontSize(18)))),
                  //             Container(child: Icon(
                  //               Icons.expand_more,
                  //               color: Color.fromRGBO(29, 180, 119, 1),
                  //             ))
                  //           ],
                  //         ),
                  //       ),
                  //       Container(
                  //         // width: MyScreenUtil.width(200),
                  //         child: Row(
                  //           mainAxisAlignment: MainAxisAlignment.start,
                  //           children: [
                  //             Container(child: Text("执行时间",style: TextStyle(fontSize: MyScreenUtil.fontSize(18)))),
                  //             Container(child: Text(
                  //               "${controller.formatTime('${detailsItem['operatorTime']}')}",
                  //               style: TextStyle(fontSize: MyScreenUtil.fontSize(16))))
                  //           ],
                  //         )
                  //       ),

                  //     ],
                  //   ),
                  // ),
                  Container(
                    child: Text(
                        "监督人：${controller.workData['supervision']['name']}",
                        style: TextStyle(fontSize: MyScreenUtil.fontSize(18))),
                  ),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: Column(
                      children: [
                        Container(
                          // width: MyScreenUtil.width(200),
                          child: Row(
                            children: [
                              Container(
                                  child: Text("检查结果",
                                      style: TextStyle(
                                          fontSize:
                                              MyScreenUtil.fontSize(18)))),
                              Container(
                                  child: Icon(
                                Icons.expand_more,
                                color: Color.fromRGBO(29, 180, 119, 1),
                              ))
                            ],
                          ),
                        ),
                        Container(
                            // width: MyScreenUtil.width(200),
                            child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                                child: Text("检查时间",
                                    style: TextStyle(
                                        fontSize: MyScreenUtil.fontSize(18)))),
                            Container(
                                child: Text(
                                    "${controller.formatTime('${detailsItem['supervisorTime']}')}",
                                    style: TextStyle(
                                        fontSize: MyScreenUtil.fontSize(16))))
                          ],
                        )),
                      ],
                    ),
                  ),
                ],
              ),
            )
          ],
        ));
  }

  // 签字方法
  signListFun(signItem) {
    return Container(
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
                border: Border.all(
                    color: Color.fromRGBO(44, 185, 129, 0.259), width: 1),
                borderRadius: BorderRadius.circular(10)),
            padding: EdgeInsets.all(MyScreenUtil.width(20)),
            margin: EdgeInsets.only(top: MyScreenUtil.width(10)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  child: Text("${signItem['role']}签字: ${signItem['name']}"),
                ),
                Container(
                  width: MyScreenUtil.width(300),
                  height: MyScreenUtil.height(100),
                  child: Image.network(signItem['imageUrl'], fit: BoxFit.cover),
                ),
                Container(
                  child: Text(
                      "签字日期: ${controller.formatTime('${signItem['date']}')}"),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  // 执行人签字方法
  executorSign(role, peData) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  color: Color.fromRGBO(216, 215, 215, 1), width: 1))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            child: Text("${role}签字: ${peData['name']}"),
          ),
          Container(
            child: Obx(() => globalController.peSign['baseImage'] != null
                ? Container(
                    width: MyScreenUtil.width(200),
                    height: MyScreenUtil.height(100),
                    child: Image.memory(
                      controller
                          .signatureEcho(globalController.peSign['baseImage']),
                      fit: BoxFit.fill,
                    ),
                  )
                : const Text("请签字")),
          ),
          Container(
              child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(bottom: 10),
                child: ElevatedButton(
                    onPressed: () {
                      controller.signAlter();
                    },
                    child: const Text("电子签名")),
              ),
              Obx(() {
                return Container(
                  child: Text(
                      "${controller.formatTime('${globalController.peSign['date']}')}"),
                );
              })
            ],
          ))
        ],
      ),
    );
  }

  // 输入值回显
  createInputComponent(detailsItem) {
    var inputComponent = [];
    for (int i = 0; i < detailsItem["amount"]; i++) {
      inputComponent.add(Container(
        margin: EdgeInsets.only(right: MyScreenUtil.height(14)),
        child: Text(
          "数值: ${detailsItem['gapPrice'][i]}",
          style: TextStyle(fontSize: MyScreenUtil.fontSize(18)),
        ),
      ));
    }
    return inputComponent;
  }
}
