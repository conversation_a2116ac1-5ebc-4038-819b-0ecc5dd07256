import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../controllers/socket_page_controller.dart'; 

class SocketPageView extends GetView<SocketPageController> {
  const SocketPageView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(MyScreenUtil.height(60)),
        child:AppBar(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children:[
              const Text("socket连接"),
              TextButton(
                onPressed: (){
                  controller.closeSocketFun();
                }, 
                child: const Text("断开所有连接",style: TextStyle(
                  color:Colors.white
                ),)
              )
            ],
          ),
          centerTitle: true,
        ),
      ),
      body: Center(
        child: Container(
          padding: EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                child: ElevatedButton(
                  onPressed: (){
                    controller.serverCreateSocket();
                  }, 
                  child:const Text("创建socket服务")
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: MyScreenUtil.height(20)),
                child: ElevatedButton(
                  onPressed: (){
                    controller.clientCreateConnect();
                  }, 
                  child:const Text("连接socket服务")
                ),
              )
              
            ],
          ),
        ),
      )
    );
  }
}
