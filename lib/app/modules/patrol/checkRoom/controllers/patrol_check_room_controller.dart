import 'dart:convert';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../controllers/global_controller.dart';
import "../../../../controllers/sqflite_controller.dart";

class PatrolCheckRoomController extends GetxController {
  SQfliteController sqfliteController = Get.find();
  GlobalController globalController = Get.find();


  RxString roomName = "".obs;
  RxString roomType = "".obs;

  /**楼层数据列表 */
  RxList roomList = [].obs;
  RxInt selectRoomStorey = 0.obs;

  
  @override
  void onInit() {
    super.onInit();
    // roomName.value = Get.arguments['roomTypeName'];
    roomName.value = "房间列表";
    // roomType.value = Get.arguments['roomType'];
    insertPatrolStartTime();
    ever(globalController.checkRoom,(_){
      roomProgress();
    });
  }

  @override
  void onReady() {
    super.onReady();
    findRoomData();
  }

  @override
  void onClose() {
    super.onClose();
  }
  
  // 插入巡检时间
  insertPatrolStartTime() async {
    var userInfoData = await sqfliteController
        .findUsers("eid='${globalController.userInfo.value!.data.eid}'");
    DateTime date = DateTime.now();
    var month = date.month < 10 ? "0${date.month}" : "${date.month}";
    var day = date.day < 10 ? "0${date.day}" : "${date.day}";
    var hour = date.hour < 10 ? "0${date.hour}" : "${date.hour}";
    var minute = date.minute < 10 ? "0${date.minute}" : "${date.minute}";
    var second = date.second < 10 ? "0${date.second}" : "${date.second}";
    print("${date.year}-${month}-${day} ${hour}:${minute}:${second}");
    if (userInfoData[0].patrolStartTime == null) {
      sqfliteController.updateUserTable(
          "user",
          {
            "patrolStartTime":
                "${date.year}-${month}-${day} ${hour}:${minute}:${second}",
            "patrolStartTimeStamp": date.millisecondsSinceEpoch
          },
          "eid='${globalController.userInfo.value!.data.eid}'");
    }
  }
  // 根据roomType查找房间
  findRoomData()async{
    var roomData = await sqfliteController.findRoomList("");
    final List<Map<String, dynamic>> roomListData = roomData.map((room) => room.toJson()).toList();
    print(roomData);
    // 按照楼层整理数据格式
    var roomDataByStorey = groupBy(roomData, (item) => item.storey);
    var result= roomDataByStorey.entries.map((entry) => {
      'storey': entry.key,
      'roomList': entry.value,
    }).toList();
    result.sort((a, b) => (a['storey'] as int).compareTo(b['storey'] as int));
    roomList.value = result;
    update();
  }

  // 更新 room 的进度
  roomProgress()async{
    var roomData = await sqfliteController.findRoomData("type='${roomType.value}'");
    final List<Map<String, dynamic>> roomListData = roomData.map((room) => room.toJson()).toList();
    // 遍历所有房间根据,查找房间下所有设备的进度状态
    for(var roomItem in roomData){
      var deviceData = await sqfliteController.findDevices("roomId='${roomItem.roomId}' AND roomType='${roomItem.type}'");
      final List<Map<String, dynamic>> deviceDataList = deviceData.map((device) => device.toJson()).toList();
      var isFinish =  deviceData.every((item) => item.isFinish == 1);
      sqfliteController.updateTable(
        "room", 
        {"isFinish":isFinish?1:0}, 
        "roomId='${roomItem.roomId}' AND type='${roomItem.type}'"
      );
    }
    // 更新完成后调用查找更新视图
    findRoomData();
  }


}
