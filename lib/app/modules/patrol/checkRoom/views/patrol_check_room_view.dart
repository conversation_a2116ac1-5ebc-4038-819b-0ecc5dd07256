import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/patrol/device/controllers/patrol_device_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';

import '../../../../controllers/global_controller.dart';
import '../controllers/patrol_check_room_controller.dart';

class PatrolCheckRoomView extends GetView<PatrolCheckRoomController> {

  PatrolCheckRoomView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
            body: Container(
          width: MyScreenUtil.width(1392),
          margin: EdgeInsets.all(MyScreenUtil.width(24)),
          child: ListView(
            children: [
              Container(
                  padding: EdgeInsets.only(
                      left: MyScreenUtil.width(24),
                      bottom: MyScreenUtil.width(24),
                      right: MyScreenUtil.width(24)),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10),
                    // border: Border(bottom: )
                  ),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () => {Get.back()},
                        child: Container(
                          // alignment: Alignment.centerLeft, // 设置垂直居中
                          height: MyScreenUtil.height(60),
                          decoration: const BoxDecoration(
                            border: Border(
                              bottom: BorderSide(
                                  width: 1, color: Color(0xFFFFeeeeee)),
                            ),
                            // color:Colors.red
                          ),
                          child: Row(
                            children: [
                              Container(
                                width: MyScreenUtil.width(24),
                                height: MyScreenUtil.height(24),
                                child: Image.asset(
                                  "assets/images/icon/left.png",
                                  fit: BoxFit.cover,
                                ),
                              ),
                              Text(controller.roomName.value)
                            ],
                          ),
                        ),
                      ),
                      Container(
                        // width: 800,
                        // height: 400,
                        child: Row(
                          children:
                              controller.roomList.asMap().entries.map((entry) {
                            int index = entry.key;
                            var item = entry.value;
                            return roomList(item,index);
                          }).toList(),
                        ),
                      ),
                      // 设备列表
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 10),
                        child: Wrap(
                          children: getRooms()
                        ),
                      )
                    ],
                  )),
            ],
          ),
        )));
  }

  List<Widget> getRooms() {
    if(controller.roomList.isEmpty) return [];
    return controller.roomList[controller.selectRoomStorey.value]
    ["roomList"]
        .map((roomListItem) {
      return roomItem(roomListItem);
    })
        .toList()
        .cast<Widget>();
  }


  // 房间列表
  Widget roomList(item,index) {
    return controller.selectRoomStorey == index
        ? InkWell(
            onTap: () {
              controller.selectRoomStorey.value = index;
            },
            child: Container(
              margin: const EdgeInsets.only(left: 20, top: 20, right: 20),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(width: 2, color: Color(0xFFFF5777FF)),
                ),
                // color:Colors.red
              ),
              // child: Row(
              //   children: [
              //     // 楼层
              //     Row(
              //       children: [
              //         Text(
              //           "${item['storey']}层",
              //           style: TextStyle(
              //               color: MyScreenUtil.FontColor(),
              //               fontSize: MyScreenUtil.fontSize(18),
              //               fontWeight: FontWeight.w600),
              //         )
              //       ],
              //     ),
              //   ],
              // ),
            ))
        : InkWell(
            onTap: () {
              controller.selectRoomStorey.value = index;
            },
            child: Container(
              margin: const EdgeInsets.only(left: 20, top: 20, right: 20),
              decoration: const BoxDecoration(
                  // color:Colors.red
                  ),
              child: Row(
                children: [
                  // 楼层
                  Row(
                    children: [
                      Text(
                        "${item['storey']}层",
                        style: TextStyle(
                            color: Color(0xFF86909C),
                            fontSize: MyScreenUtil.fontSize(18),
                            fontWeight: FontWeight.w600),
                      )
                    ],
                  ),
                ],
              ),
            ),
          );
  }

  // 房间项
  roomItem(roomListItem) {
    return InkWell(
      onTap: () {
        Get.toNamed("/device", arguments: {
          "roomName": "${roomListItem.name}-${roomListItem.roomId}",
          "roomType": "${roomListItem.type}",
          "roomId": "${roomListItem.roomId}",
          "name": "${roomListItem.name}"
        });
        // Get.put(PatrolDeviceController());
      },
      child: Container(
        key: ValueKey(roomListItem.roomId),
        margin: const EdgeInsets.only(right: 10, bottom: 10),
        width: MyScreenUtil.width(256),
        height: MyScreenUtil.height(160),
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(
            // border: Border.all(
            //     width: 1.0, color: const Color.fromARGB(255, 214, 214, 214)),
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Color.fromRGBO(0, 0, 0, 0.1),
                offset: Offset(0, 0),
                blurRadius: 5,
                spreadRadius: 0,
              ),
            ],
            borderRadius: BorderRadius.circular(10)),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        margin: const EdgeInsets.only(right: 10),
                        child: Image.asset(
                          "assets/images/icon/room_title.png",
                          fit: BoxFit.fill,
                        ),
                      ),
                      Text(
                        "${roomListItem.roomId}",
                        style: TextStyle(
                            color: MyScreenUtil.FontColor(),
                            fontSize: MyScreenUtil.fontSize(18),
                            fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                Container(
                    child: roomListItem.isFinish == 1
                        ? Text(
                            "已完成",
                            style: TextStyle(
                                fontSize: MyScreenUtil.fontSize(12),
                                color: const Color.fromRGBO(52, 196, 71, 1)),
                          )
                        : Container(
                            child: Row(children: [
                              Container(
                                width: MyScreenUtil.width(6),
                                height: MyScreenUtil.height(6),
                                margin: const EdgeInsets.only(right: 10),
                                child: Image.asset(
                                  "assets/images/icon/dian.png",
                                  fit: BoxFit.fill,
                                ),
                              ),
                              Text(
                                "未完成",
                                style: TextStyle(
                                    fontSize: MyScreenUtil.fontSize(12),
                                    color: Color(0xFF2F303A)),
                              ),
                            ]),
                          )),
              ],
            ),
            Container(
              alignment: Alignment.centerLeft,
              margin: const EdgeInsets.only(top: 20),
              child: Text(
                "${roomListItem.name}",
                style: TextStyle(fontSize: MyScreenUtil.fontSize(16)),
              ),
            ),
            Container(
              alignment: Alignment.centerLeft,
              margin: const EdgeInsets.only(top: 5),
              child: Text(
                "设备数量: ${roomListItem.deviceCount}",
                style: TextStyle(fontSize: MyScreenUtil.fontSize(16)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
