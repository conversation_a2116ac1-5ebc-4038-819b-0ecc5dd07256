import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/global_controller.dart';
import "../../../../controllers/sqflite_controller.dart";
import "../../../../controllers/global_controller.dart";

class PatrolRecordController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  // 站位监听变量
  RxString titleName = "巡检记录".obs;

  // 数据响应
  RxList historyList = [].obs;

  @override
  void onInit()async{
    super.onInit();
    await findHistoryList();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 获取巡检记录
  findHistoryList()async{
    var historyData =  await sqfliteController.findHistory("eid='${globalController.userInfo.value!.data.eid}'");
    // final List<Map<String, dynamic>> historyData = historyList.map((item) => item.toJson()).toList();
    historyList.value = historyData.reversed.toList();
    update();
  }

}
