import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/screenutil.dart';

class IrDialog {

  /// 普通对话框， 两个按钮
   static void showDialog(String tip, String message , String confirmButtonText , String cancelText , {VoidCallback? confirmCall,
          VoidCallback? cancelCall}) {

     Get.dialog(
         barrierDismissible: false,
         AlertDialog(
           title: Row(
             mainAxisAlignment: MainAxisAlignment.center,
             children: [Text(tip)],
           ),
           content: Row(
             children: [Text("${message}", style: TextStyle(fontSize: 13),)],
           ),
           actions: [
             ElevatedButton(
                 onPressed: () async {
                   Get.back();
                   confirmCall?.call();
                 },
                 child: Text(confirmButtonText),
                 style: ButtonStyle(backgroundColor:
                 MaterialStateProperty.resolveWith((states) {
                   return MyScreenUtil.ThemColor();
                 }))),
             Container(
               width: 15,
             ),
             ElevatedButton(
               onPressed: () {
                 cancelCall?.call();
                 Get.back();
               },
               child: Text(cancelText),
               style: ButtonStyle(
                 backgroundColor: MaterialStateProperty.all<Color>(
                     Color.fromRGBO(143, 147, 153, 1)),
               ),
             ),
           ],
         ));

   }

}