

import 'package:get/get.dart';

import '../../../controllers/global_controller.dart';
import '../../../controllers/sqflite_controller.dart';
import '../../../data/user.dart';

class IrTaskController extends GetxController {

  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  // 房间类型列表
  RxList roomTypeInfoList = [].obs;

  var isStartModel = true;

  // 用户基本信息
  Rx<UserInfo> userInfo = Rx<UserInfo>(const UserInfo(
      eid: "",
      avatar: "",
      name: "",
      phone: "",
      passWorld: "",
      deptName: "",
      technicalPost: "",
      livingPlace: "",
      gender: 0,
      occupation: "",
      roomTypeName: "",
      createTime: 0,
      floor: "",
      shifts: "",
      roomType: "",
      isUpload: 0,
      patrolStartTimeStamp: 0,
      patrolEndTimeStamp: 0,
      companyId:"",
      companyName:"",
      deviceMaintainPer: 0
  ));


  @override
  void onInit() {
    super.onInit();

  }

  // db 操作
  _fetchTaskList() {

  }

}