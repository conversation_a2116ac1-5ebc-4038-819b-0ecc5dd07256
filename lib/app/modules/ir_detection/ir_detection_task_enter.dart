import 'package:flutter/material.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';
import '../../utils/screenutil.dart';


/// 【新】主页红外温度入口（检查任务， 检查记录， 上传数据）  2024-06-03
class IrTaskEnter extends StatelessWidget {

  final VoidCallback? refreshAction;
  final VoidCallback? startAction;  // 检查任务
  // final VoidCallback? endAction;
  final VoidCallback? recordAction;   // 检查记录

  const IrTaskEnter({super.key , this.refreshAction , this.startAction , this.recordAction});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 20),
      padding: EdgeInsets.only(
          top: 10,
          left: (14),
          right: (14),
          bottom: 20
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14.0),
        color: const Color(0xFFFFFFFF),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment:
            MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: Row(
                  children: [
                    Container(
                      width: (5),
                      height: (20),
                      margin: const EdgeInsets
                          .only(right: 13),
                      decoration:
                      BoxDecoration(
                        borderRadius:
                        BorderRadius
                            .circular(
                            14.0),
                        color:
                        Color(0xFF5777FF),
                      ),
                    ),
                    Text(
                      "红外检测",
                      style: TextStyle(
                          color: const Color(
                              0xFF2F303A),
                          fontSize: 17,
                          fontWeight:
                          FontWeight.w500),
                    ),
                  ],
                ),
              ),
              Expanded(child: Container()),
              Container(
                child: TextButton.icon(
                  onPressed: () {
                    refreshAction?.call();
                  },
                  icon: Icon(
                    Icons.refresh_sharp,
                    size: 20,
                    color: MyScreenUtil
                        .ThemColor(),
                  ),
                  label: Text(
                    "更新",
                    style: TextStyle(
                        fontSize: 16,
                        color: MyScreenUtil
                            .ThemColor()),
                  ),
                ),
              )

            ],
          ),
          Row(
            children: [
              item("检查任务" , AssetsRes.PATROLA, () => startAction?.call()),
              // 25.gap,
              // item("结束检测" ,AssetsRes.PATROLB, () => endAction?.call()),
              25.gap,
              item("检测记录" ,AssetsRes.PATROLC, () => recordAction?.call()),
            ],
          ),
        ],
      ),
    );
  }

   Widget item(String label , String imagePath, VoidCallback onClick) {
    return InkWell(
      onTap: () => onClick.call(),
      child: Container(
        margin: EdgeInsets.only(top: (10)),
        padding: EdgeInsets.all((16)),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.08),
              blurRadius: 10,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          //   crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: (40),
              height: (40),
              margin: EdgeInsets.only(right: (8)),
              child: Image.asset("$imagePath"),
            ),
            Text(label,
                style: TextStyle(
                  fontSize: (15),
                )),
          ],
        ),
      ),
    );
   }
}