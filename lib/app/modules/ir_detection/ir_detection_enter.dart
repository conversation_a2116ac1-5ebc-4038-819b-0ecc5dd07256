import 'package:flutter/material.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';
import '../../utils/screenutil.dart';


/// 主页红外温度入口（开始检测， 结束检测， 上传数据）
class IrTemperatureEnter extends StatelessWidget {

  final VoidCallback? refreshAction;
  final VoidCallback? startAction;
  final VoidCallback? endAction;
  final VoidCallback? uploadAction;

  const IrTemperatureEnter({super.key , this.refreshAction , this.startAction ,
    this.endAction , this.uploadAction});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 20),
      padding: EdgeInsets.only(
          top: 10,
          left: MyScreenUtil.width(24),
          right: MyScreenUtil.width(24),
          bottom: 20
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14.0),
        color: const Color(0xFFFFFFFF),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment:
            MainAxisAlignment.spaceBetween,
            children: [
              Container(
                child: Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(
                          5),
                      height: MyScreenUtil.height(
                          20),
                      margin: const EdgeInsets
                          .only(right: 13),
                      decoration:
                      BoxDecoration(
                        borderRadius:
                        BorderRadius
                            .circular(
                            14.0),
                        color:
                        Color(0xFF5777FF),
                      ),
                    ),
                    Text(
                      "红外检测",
                      style: TextStyle(
                          color: const Color(
                              0xFF2F303A),
                          fontSize:
                          MyScreenUtil
                              .fontSize(
                              18),
                          fontWeight:
                          FontWeight
                              .w500),
                    ),
                  ],
                ),
              ),
              Expanded(child: Container()),
              Container(
                child: TextButton.icon(
                  onPressed: () {
                    refreshAction?.call();
                  },
                  icon: Icon(
                    Icons.refresh_sharp,
                    size:
                    MyScreenUtil.fontSize(
                        20),
                    color: MyScreenUtil
                        .ThemColor(),
                  ),
                  label: Text(
                    "更新",
                    style: TextStyle(
                        fontSize: MyScreenUtil
                            .fontSize(16),
                        color: MyScreenUtil
                            .ThemColor()),
                  ),
                ),
              )

            ],
          ),
          Row(
            children: [
              item("开始检测" , AssetsRes.PATROLA, () => startAction?.call()),
              25.gap,
              item("结束检测" ,AssetsRes.PATROLB, () => endAction?.call()),
              25.gap,
              item("上传数据" ,AssetsRes.PATROLC, () => uploadAction?.call()),
            ],
          ),
        ],
      ),
    );
  }

   Widget item(String label , String imagePath, VoidCallback onClick) {
    return InkWell(
      onTap: () => onClick.call(),
      child: Container(
        width: MyScreenUtil.width(210),
        height: MyScreenUtil.height(105),
        margin: EdgeInsets.only(top: MyScreenUtil.height(10)),
        padding: EdgeInsets.all(MyScreenUtil.height(16)),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.0),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.08),
              blurRadius: 10,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Row(
          //   crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: MyScreenUtil.width(73),
              height: MyScreenUtil.height(73),
              margin: EdgeInsets.only(right: MyScreenUtil.width(12)),
              child: Image.asset("$imagePath"),
            ),
            Text(label,
                style: TextStyle(
                  fontSize: MyScreenUtil.fontSize(20),
                )),
          ],
        ),
      ),
    );
   }
}