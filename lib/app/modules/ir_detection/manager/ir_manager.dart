import 'package:sjzx_patrol_system_mobile/app/db/ir_task/ir_task.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/entity/ir_permission/ir_task_resp.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/ir_datasource.dart';

import '../../../../main.dart';
import '../../../api/env_config.dart';
import '../../../db/db_helper.dart';
import '../../../retrofit/entity/ir_permission/IrDeviceListItem.dart';
import '../model/ir_convert.dart';

class IrManager {

   static Future resetIrData (String userId) async {
     List<IrTaskRespItem> taskListWithDevices = await _fetchIrDeviceList(userId);
     // 转换为任务
     List<IrTask> taskList = await taskListWithDevices.map((e) => convert2UiTask(userId ,e)).toList();

     var dbchest = await convert2IrChest(userId, taskListWithDevices);
     var devices = await convert2IrChestDevices(userId, taskListWithDevices);
     // 删除 task , chest； ir devices ， ir state
     await DBHelper.clearTasks(userId);
     await DBHelper.clearChestList(userId);
     await DBHelper.clearChestDeviceList(userId);
     await DBHelper.clearIrState(userId);
     // 插入新数据
     await DBHelper.insertTaskList(taskList);
     await DBHelper.insertChestList(dbchest);
     await DBHelper.insertChestDevices(devices);
     await DBHelper.insertIrState(userId);
   }

   // 为保证UI 视图 数据一致， 接口有变化后需要 强转 data
   static Future<List<IrTaskRespItem>> _fetchIrDeviceList(String userId) async {
     var userDataSource = IrDataSource(retrofitDio , baseUrl: Host.userApi);
     var l =  await userDataSource.getIrTaskList(userId);
     if(!l.success()) return [];
     List<IrTaskRespItem> respList = await l.data;
     return respList;
   }

}