import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/upload_data/controllers/ir_uploaddata_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/time_helper.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

/// 上传数据
class IrUploadDataView extends StatelessWidget {
  const IrUploadDataView({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
          child: Container(
        margin: const EdgeInsets.all(20),
        child: Column(
          children: [
            InkWell(
              onTap: () => {Get.back()},
              child: Container(
                padding: EdgeInsets.only(
                    left: MyScreenUtil.width(24),
                    right: MyScreenUtil.width(24)),
                // alignment: Alignment.centerLeft, // 设置垂直居中
                height: MyScreenUtil.height(60),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Row(
                  children: [
                    Container(
                      width: MyScreenUtil.width(24),
                      height: MyScreenUtil.height(24),
                      child: Image.asset(
                        "assets/images/icon/left.png",
                        fit: BoxFit.cover,
                      ),
                    ),
                    Text('上传数据')
                  ],
                ),
              ),
            ),

            GetBuilder<IrUploadDataController>(builder: (controller){
               return  Expanded(
                 flex: 1,
                 child: Container(
                   margin: EdgeInsets.only(
                       bottom: MyScreenUtil.height(24),
                       top: MyScreenUtil.height(24)),
                   padding: EdgeInsets.all(MyScreenUtil.height(24)),
                   decoration: BoxDecoration(
                       color: Colors.white,
                       borderRadius: BorderRadius.circular(16)),
                   child: Column(
                     mainAxisSize: MainAxisSize.max,
                     children: [
                       Container(
                           margin: EdgeInsets.only(
                             bottom: MyScreenUtil.height(24),
                           ),
                           child: Row(
                             children: [
                               Container(
                                 width: MyScreenUtil.width(5),
                                 height: MyScreenUtil.height(20),
                                 margin: const EdgeInsets.only(right: 13),
                                 decoration: BoxDecoration(
                                   borderRadius: BorderRadius.circular(14.0),
                                   color: Color(0xFF5777FF),
                                 ),
                               ),
                               Text(
                                 "待上传数据列表",
                                 style: TextStyle(
                                     fontSize: MyScreenUtil.fontSize(18),
                                     fontWeight: FontWeight.w600),
                               )
                             ],
                           )),
                       Expanded(flex: 1, child: DataList()),
                       controller.uiList.isEmpty ? Container(): Container(
                         width: double.infinity,
                         height: MyScreenUtil.height(60),
                         decoration: BoxDecoration(
                             color: MyScreenUtil.FontColor(),
                             borderRadius:
                             BorderRadius.circular(MyScreenUtil.radius(16))),
                         child: ElevatedButton(
                             onPressed: () {
                               controller.uploadAction();
                             },
                             style: ButtonStyle(backgroundColor:
                             MaterialStateProperty.resolveWith((states) {
                               return MyScreenUtil.FontColor();
                             })),
                             child: const Text("数据上传")),
                       )
                     ],
                   ),
                 ),
               );
            }),
          ],
        ),
      )),
    );
  }

  // 待上传数据列表
  Widget DataList() {

    return GetBuilder<IrUploadDataController>(builder: (controller) {
      return ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: 1,
        itemBuilder: (BuildContext context, int index) {
          return controller.uiList.isEmpty ? _buildEmptyTip() :
          _buildToUploadItem(controller.uiList[0]);
        },);
    });
  }

  Widget _buildEmptyTip() {
    return Container(
      child: const Text("当前没有要上传的数据"),
    );
  }

  Widget _buildToUploadItem (ToDoUploadUIItem item) {
     return Container(
       margin: EdgeInsets.only(bottom: 50),
       padding: EdgeInsets.all(20),
       decoration: BoxDecoration(
           color: Color(0xffF6F8FA),
           borderRadius: BorderRadius.circular(
               MyScreenUtil.radius(10)),
           ),
       child: Column(
         mainAxisSize: MainAxisSize.min,
         crossAxisAlignment: CrossAxisAlignment.center,
         children: [
           // 头像
           Container(
             width: MyScreenUtil.width(100),
             height: MyScreenUtil.width(100),
             decoration: BoxDecoration(
                 borderRadius: BorderRadius.circular(
                     MyScreenUtil.radius(120)),
                 // color:MyScreenUtil.ThemColor(),
                 image: StringUtil.isEmpty(item.avatar)
                     ? const DecorationImage(
                     image: AssetImage(
                         "assets/images/userLogo.png"),
                     fit: BoxFit.fill)
                     : DecorationImage(
                     image: NetworkImage(
                         "${item.avatar ?? ''}"),
                     fit: BoxFit.fill)),
           ),
           16.gap,
           Text(item.userName ?? '' , style: const TextStyle(fontSize: 18 ,color: Color(0xff5777FF))),
           16.gap,
           Row(
             mainAxisSize: MainAxisSize.min,
             children: [
               Image.asset(AssetsRes.TIME ,width: 20,),
               6.gap,
               const Text('开始时间', style: TextStyle(fontSize: 16 ,color: Color(0xff2F303A)),),
             ],
           ),
           5.gap,
           Text(TimeHelper.timeStamp2FullFormat(item.startTime) , style: const TextStyle(fontSize: 16, color: Color(0xff86909C)),),
           16.gap,
           Row(
             mainAxisSize: MainAxisSize.min,
             children: [
               Image.asset(AssetsRes.TIME , width: 20,),
               6.gap,
               const Text('结束时间', style: TextStyle(fontSize: 16 ,color: Color(0xff2F303A)),),
             ],
           ),
           5.gap,
           Text(TimeHelper.timeStamp2FullFormat(item.endTime) , style: const TextStyle(fontSize: 16, color: Color(0xff86909C)),),
           5.gap,
           Text('检测的设备： ${item.devicesCount ?? 0}' , style: const TextStyle(fontSize: 16, color: Color(0xff86909C)),),
           16.gap,
           Row(
             mainAxisSize: MainAxisSize.min,
             mainAxisAlignment: MainAxisAlignment.center,
             crossAxisAlignment: CrossAxisAlignment.center,
             children: [
               Row(
                 children: [
                   Container(
                     width: MyScreenUtil.width(24),
                     height: MyScreenUtil.height(24),
                     child: Image.asset(
                       AssetsRes.POSITION_BLUE,
                       fit: BoxFit.cover,
                     ),
                   ),
                   Text(
                     item.position ?? '暂无',
                     style: TextStyle(
                         color: MyScreenUtil.FontColor()),
                   )
                 ],
               ),
               10.gap,
               Container(
                 child: Row(
                   children: [
                     Container(
                       width: MyScreenUtil.width(24),
                       height: MyScreenUtil.height(24),
                       child: Image.asset(AssetsRes.DEPARTMENT_BLUE,
                         fit: BoxFit.cover,
                       ),
                     ),
                     Text(item.departMentName ?? '暂无',
                       style: TextStyle(
                           color: MyScreenUtil.FontColor()),
                     )
                   ],
                 ),
               ),
               10.gap,
               Container(
                 child: Row(
                   children: [
                     Container(
                       width: MyScreenUtil.width(24),
                       height: MyScreenUtil.height(24),
                       child: Image.asset(AssetsRes.SPECIALITY_BLUE,
                         fit: BoxFit.cover,
                       ),
                     ),
                     Text(
                       item.technicalPost ?? '暂无',
                       style: TextStyle(
                           color: MyScreenUtil.FontColor()),
                     )
                   ],
                 ),
               ),
             ],
           )
         ],
       ),
     );
  }

}
