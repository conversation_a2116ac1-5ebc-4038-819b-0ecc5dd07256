import 'dart:io';

import 'package:collection/collection.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_ir_plugin/ir/surface_platform_plugin.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/file_util.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';

import '../../../controllers/global_controller.dart';
import '../../../utils/screenutil.dart';

/// ir 红外 设备管理页面
class IrDeviceCheckController extends GetxController {

  GlobalController globalController = Get.find();

  TextEditingController textEditingController = TextEditingController();

  TextEditingController imageNameTextEditingController = TextEditingController();

  List<IrChestDevice> uiDevices = [];

  IrChestDevice? currentDevice;

  String chestId = "";
  String userId = "";

  String imagePathPrefix = ''; // 图片路径前缀


  @override
  void onInit() {
    super.onInit();
    userId = globalController.queryCurrentUserId() ?? '';
    chestId = Get.arguments['chestId'] ?? '';
    createModels();
  }

  @override
  void onReady() {
    super.onReady();
    try{

      SurfacePlatformPlugin.init((temperature) {
        updateCurrentTemperature(temperature);
      }, (path) {
        oldImagePath = path;
        imageNameTextEditingController.text = realFileName(oldImagePath);
        _showModifyPictureNameDialog(Get.context!);
      });

    }catch(e){
      print(e);
    }
  }

  String realFileName(String path) {
    if(StringUtil.isEmpty(path)) return '';
    var picName = path.split('/').lastOrNull ?? '';
    return picName;
  }

  String joinPath(String oldPath ,String fileName) {
    int lastSlashIndex = oldPath.lastIndexOf('/');
    String directoryPath = oldPath.substring(0, lastSlashIndex);
    String newPath = directoryPath + fileName;
    logger('新路径$newPath');
    return newPath;
  }

  createModels() async {
    uiDevices.clear();

    var deviceList = await DBHelper.getIrDeviceList(userId ,chestId);

    uiDevices.addAll(deviceList);

    if(uiDevices.isNotEmpty){
      uiDevices.first.selected = true;
      currentDevice = uiDevices.first;
    }
    
    update();
  }

  void selectItem(IrChestDevice item) {
    FocusManager.instance.primaryFocus?.unfocus();
    item.selected = true;
    uiDevices.forEach((element) {
      if(item.irDeviceId == element.irDeviceId){
        element.selected = true;
        currentDevice = element;
      }else{
        element.selected = false;
      }
    });
    update();
    _finishEditContent();
  }

  void nextDevice() {
    if(uiDevices.isEmpty) return;
    var mIndex = 0;
    uiDevices.forEachIndexed((index, element) {
      if(currentDevice?.irDeviceId == element.irDeviceId){
        mIndex = index;
        return;
      }
    });
    mIndex ++;
    if(mIndex  >= uiDevices.length){
      mIndex = 0;
    }
    currentDevice = uiDevices[mIndex];
    if(currentDevice != null){
      selectItem(currentDevice!);
    }
    update();
    _finishEditContent();
  }

  // 调用 native 拍照方法
  void takePhoto(BuildContext context , VoidCallback showDialogF) async {
    try{
      var temperature = await SurfacePlatformPlugin.takePhoto();
    }catch(e){
      logger('takePhoto...$e');
    }
  }

  void updateCurrentTemperature(int temperature) {
    currentDevice?.checkTemperature = temperature.toString();
    update();
  }

  void updateCurrentShot(String imagePath) {
    if(StringUtil.isEmpty(imagePath)){
      toast('图片地址不存在');
      return;
    }

    currentDevice?.path = imagePath ?? '';
    update();
    _updateIrDevice2Db();
    toast('保存成功');
  }

  void lastDevice() {
    if(uiDevices.isEmpty) return;
    var mIndex = 0;
    uiDevices.forEachIndexed((index, element) {
      if(currentDevice?.irDeviceId == element.irDeviceId){
        mIndex = index;
        return;
      }
    });
    mIndex --;
    if(mIndex < 0){
      mIndex = uiDevices.length -1;
    }
    currentDevice = uiDevices[mIndex];
    if(currentDevice != null){
      selectItem(currentDevice!);
    }
    update();
    _finishEditContent();
  }


  int getCurrentIndex() {
    if(currentDevice == null) return -1;
    var mIndex = -1;
    uiDevices.forEachIndexed((index, element) {
      if(currentDevice!.irDeviceId == element.irDeviceId){
        mIndex = index;
        return;
      }
    });
    return mIndex;
  }

   Future _finishEditContent() async {
    textEditingController.clear();
    textEditingController.text = currentDevice?.comments ??'';

    await _updateIrDevice2Db();
    update();
  }

  /// 有温度和备注， 1 更新当前数据库， 2 更新进度
  _updateIrDevice2Db() async {
    if(currentDevice == null) return;
    if(StringUtil.isEmpty(currentDevice?.checkTemperature) || StringUtil.isEmpty(currentDevice?.path)) return;
    var detectionTime = DateTime.now().millisecondsSinceEpoch;
    await DBHelper.updateIrDevice(userId ,currentDevice!.irDeviceId , currentDevice!.checkTemperature , currentDevice!.comments ?? '',
        currentDevice!.path,
        detectionTime);
    var chestDeviceList = await DBHelper.getIrDeviceList(userId, chestId);
    if(chestDeviceList.isNotEmpty){
      var finishCount = chestDeviceList.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList().length;
      if(finishCount == 0) return;
      var d = finishCount/chestDeviceList.length;
      var progress = _formatPercentage(d).toString();

      DBHelper.updateIrChestProgress(userId, chestId, progress);
    }
  }

  int _formatPercentage(double number) {
    return (number * 100).round();
  }

  // 备注
  void onCommentChanged(String comments) async {
     currentDevice?.comments = comments;
     uiDevices.forEach((element) {
        if(currentDevice?.irDeviceId == element.irDeviceId){
          element.comments = comments;
          return;
        }
     });
     update();

     // await _finishEditContent();
  }

  var oldImagePath = '';

  reNameFile(String newFileName) async {
    logger('old:$oldImagePath ,new: $newFileName');
    if(newFileName == realFileName(oldImagePath)) {
      updateCurrentShot(oldImagePath);
      return;
    };
    try{
      var newPath = joinPath(oldImagePath , newFileName);
      await File(oldImagePath).copy(newPath);
      File(oldImagePath).delete();
      updateCurrentShot(newPath);
    }catch(e){
      logger(e);
    }
  }

  // 确认拍照图片名称
  _showModifyPictureNameDialog(BuildContext context) async {
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [Text("提示")],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("确认图片名称，可编辑修改"),
              TextField(
                controller: imageNameTextEditingController,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              )
            ],

          ),
          actions: [
            ElevatedButton(
                onPressed: () {
                  Get.back();
                  reNameFile(imageNameTextEditingController.text);
                },
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                })),
                child: const Text("确认")),

            ElevatedButton(
                onPressed: () async {
                  // 删除图片
                  await _deleteCurrentPicFile(oldImagePath);
                  Get.back();
                },
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                })),
                child: const Text("取消", style: TextStyle(color: Colors.black),)),
          ],
        ));
  }


  Future _deleteCurrentPicFile(String filePath) async {
     try{
       logger('删除文件 $filePath');
       await FileUtil.deleteFileByPath(filePath);
     }catch(e){}
  }


}