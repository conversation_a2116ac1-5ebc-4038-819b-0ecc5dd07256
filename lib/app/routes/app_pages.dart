import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/device_check/ir_device_check_view.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/start_patrol/views/ir_patrol_start_view.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/camera/camera_binding.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/camera/camera_view.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/chest/ir_chest_binding.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/chest/ir_chest_view.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/device_list/ir_device_list_binding.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/device_list/ir_device_list_view.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/home/<USER>';
import 'package:sjzx_patrol_system_mobile/app/phone/login_phone/login_page.dart';

import '../modules/home/<USER>/home_binding.dart';
import '../modules/home/<USER>/home_view.dart';
import '../modules/ir_detection/device_check/ir_device_check_binding.dart';
import '../modules/ir_detection/start_patrol/bindings/ir_patrol_start_binding.dart';
import '../modules/ir_detection/upload_data/bindings/ir_uploaddata_binding.dart';
import '../modules/ir_detection/upload_data/views/ir_uploaddata_view.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/patrol/checkRoom/bindings/patrol_check_room_binding.dart';
import '../modules/patrol/checkRoom/views/patrol_check_room_view.dart';
import '../modules/patrol/device/bindings/patrol_device_binding.dart';
import '../modules/patrol/device/views/patrol_device_view.dart';
import '../modules/patrol/endTask/bindings/patrol_end_task_binding.dart';
import '../modules/patrol/endTask/views/patrol_end_task_view.dart';
import '../modules/patrol/record/bindings/patrol_record_binding.dart';
import '../modules/patrol/record/views/patrol_record_view.dart';
import '../modules/patrol/start/bindings/patrol_start_binding.dart';
import '../modules/patrol/start/views/patrol_start_view.dart';
import '../modules/patrol/update/bindings/patrol_update_binding.dart';
import '../modules/patrol/update/views/patrol_update_view.dart';
import '../modules/socketPage/bindings/socket_page_binding.dart';
import '../modules/socketPage/views/socket_page_view.dart';
import '../modules/uphold/examine/bindings/uphold_examine_binding.dart';
import '../modules/uphold/examine/views/uphold_examine_view.dart';
import '../modules/uphold/record/bindings/uphold_record_binding.dart';
import '../modules/uphold/record/views/uphold_record_view.dart';
import '../modules/uphold/startExamine/bindings/uphold_start_examine_binding.dart';
import '../modules/uphold/startExamine/views/uphold_start_examine_view.dart';
import '../modules/uphold/startUphold/bindings/uphold_start_uphold_binding.dart';
import '../modules/uphold/startUphold/views/uphold_start_uphold_view.dart';
import '../modules/uphold/worlOrder/bindings/uphold_worl_order_binding.dart';
import '../modules/uphold/worlOrder/views/uphold_worl_order_view.dart';
import '../phone/home/<USER>';
import '../phone/login_phone/login_phone_binding.dart';
import '../phone/task/phone_ir_task_binding.dart';
import '../phone/task/phone_ir_task_page.dart';

part 'app_routes.dart';


isLogin() {
  return true;
}

class AppPages {
  AppPages._();

  static var INITIAL = Routes.HOME;

  static final routes = [
    GetPage(
      name: _Paths.HOME,
      page: () => HomeView(),
      binding: HomeBinding(),
      // middlewares: [AuthGuard()], // 添加路由守卫
    ),
    GetPage(
      name: _Paths.LOGIN,
      page: () => LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.PATROL_START,
      page: () => PatrolStartView(),
      binding: PatrolStartBinding(),
    ),
    GetPage(
      name: _Paths.PATROL_CHECK_ROOM,
      page: () => PatrolCheckRoomView(),
      binding: PatrolCheckRoomBinding(),
    ),
    GetPage(
      name: _Paths.PATROL_DEVICE,
      page: () => PatrolDeviceView(),
      binding: PatrolDeviceBinding(),
    ),
    GetPage(
      name: _Paths.PATROL_END_TASK,
      page: () => const PatrolEndTaskView(),
      binding: PatrolEndTaskBinding(),
    ),
    GetPage(
      name: _Paths.PATROL_UPDATE,
      page: () => const PatrolUpdateView(),
      binding: PatrolUpdateBinding(),
    ),
    GetPage(
      name: _Paths.PATROL_RECORD,
      page: () => const PatrolRecordView(),
      binding: PatrolRecordBinding(),
    ),
    GetPage(
      name: _Paths.UPHOLD_WORL_ORDER,
      page: () => UpholdWorlOrderView(),
      binding: UpholdWorlOrderBinding(),
    ),
    GetPage(
      name: _Paths.UPHOLD_RECORD,
      page: () => const UpholdRecordView(),
      binding: UpholdRecordBinding(),
    ),
    GetPage(
      name: _Paths.UPHOLD_EXAMINE,
      page: () => const UpholdExamineView(),
      binding: UpholdExamineBinding(),
    ),
    GetPage(
      name: _Paths.UPHOLD_START_UPHOLD,
      page: () => UpholdStartUpholdView(),
      binding: UpholdStartUpholdBinding(),
    ),
    GetPage(
      name: _Paths.UPHOLD_START_EXAMINE,
      page: () => const UpholdStartExamineView(),
      binding: UpholdStartExamineBinding(),
    ),
    GetPage(
      name: _Paths.SOCKET_PAGE,
      page: () => const SocketPageView(),
      binding: SocketPageBinding(),
    ),

    GetPage(
        name: _Paths.IR_START_PATROL,
        page: () => IrPatrolStartView(),
        binding: IrPatrolStartBinding(),
    ),

    GetPage(
      name: _Paths.IR_UPLOAD_DATA,
      page: () => IrUploadDataView(),
      binding: IrUploadDataBindins(),
    ),

    GetPage(
      name: _Paths.IR_DEVICE_CHECK,
      page: () => IrDeviceCheckView(),
      binding: IrDeviceCheckBindings(),
    ),

    GetPage(
      name: _Paths.IR_TASK,
      page: () => PhoneIrTaskPage(),
      binding: PhoneIrTaskBinding(),
    ),

    // phone
    GetPage(
      name: _Paths.PHONE_LOGIN,
      page: () => LoginPhonePage(),
      binding: PhoneLoginBinding(),
    ),

    GetPage(
      name: _Paths.PHONE_HOME,
      page: () => HomePhonePage(),
      binding: PhoneHomeBinding(),
    ),

    GetPage(
      name: _Paths.PHONE_CHEST,
      page: () => IrChestView(),
      binding: IrChestBinding(),
    ),

    GetPage(
      name: _Paths.PHONE_DEVICES,
      page: () => IrDeviceListView(),
      binding: IrDeviceListBindings(),
    ),

    GetPage(
      name: _Paths.PHONE_CAMERA,
      page: () => CameraView(),
      binding: CameraBinding(),
    ),

  ];
}

// class AuthGuard extends GetMiddleware {
//   @override
//   RouteSettings? redirect(String? route){
//     GlobalController globalController = Get.find();
//     print(globalController.userInfo);
//     if (globalController.userInfo.length == 0) {
//       print("判断登录为空");
//       // 判断是否登录
//       return const RouteSettings(name: '/login'); // 跳转到登录页面
//     }
//     return null; // 允许路由跳转
//   }
// }
