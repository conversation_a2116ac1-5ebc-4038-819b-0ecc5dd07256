part of 'app_pages.dart';
// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

abstract class Routes {
  Routes._();
  static const HOME = _Paths.HOME;
  static const LOGIN = _Paths.LOGIN;
  static const PATROL_START = _Paths.PATROL_START;
  static const PATROL_CHECK_ROOM = _Paths.PATROL_CHECK_ROOM;
  static const PATROL_DEVICE = _Paths.PATROL_DEVICE;
  static const PATROL_END_TASK = _Paths.PATROL_END_TASK;
  static const PATROL_UPDATE = _Paths.PATROL_UPDATE;
  static const PATROL_RECORD = _Paths.PATROL_RECORD;
  static const UPHOLD_WORL_ORDER = _Paths.UPHOLD_WORL_ORDER;
  static const UPHOLD_RECORD = _Paths.UPHOLD_RECORD;
  static const UPHOLD_EXAMINE = _Paths.UPHOLD_EXAMINE;
  static const UPHOLD_START_UPHOLD = _Paths.UPHOLD_START_UPHOLD;
  static const UPHOLD_START_EXAMINE = _Paths.UPHOLD_START_EXAMINE;
  static const SOCKET_PAGE = _Paths.SOCKET_PAGE;
  static const IR_START_PATROL = _Paths.IR_START_PATROL;
  static const IR_UPLOAD_DATA = _Paths.IR_UPLOAD_DATA;
  static const IR_DEVICE_CHECK = _Paths.IR_DEVICE_CHECK;
  static const IR_TASK = _Paths.IR_TASK;
  static const PHONE_LOGIN = _Paths.PHONE_LOGIN;
  static const PHONE_HOME = _Paths.PHONE_HOME;
  static const PHONE_CHEST = _Paths.PHONE_CHEST;
  static const PHONE_DEVICES = _Paths.PHONE_DEVICES;
  static const PHONE_CAMERA = _Paths.PHONE_CAMERA;
  static const PHONE_PATROL_START = _Paths.PHONE_PATROL_START;
  static const PHONE_PATROL_RECORD = _Paths.PHONE_PATROL_RECORD;
}

abstract class _Paths {
  _Paths._();
  static const HOME = '/home'; // 首页
  static const LOGIN = '/login'; // 登录
  static const PATROL_START = '/start'; // 开始巡检
  static const PATROL_CHECK_ROOM = '/check-room'; //选择房间
  static const PATROL_DEVICE = '/device'; // 检查设备
  static const PATROL_END_TASK = '/end-task'; // 结束巡检
  static const PATROL_UPDATE = '/update'; // 上传数据
  static const PATROL_RECORD = '/record'; // 巡检记录
  static const UPHOLD_WORL_ORDER = '/uphold-worl-order'; // 维护工单
  static const UPHOLD_RECORD = '/uphold-record'; // 维护记录
  static const UPHOLD_EXAMINE = '/uphold-examine'; // 工单审核
  static const UPHOLD_START_UPHOLD = '/start-uphold'; // 开始维护
  static const UPHOLD_START_EXAMINE = '/start-examine'; // 开始审核
  static const SOCKET_PAGE = '/socket-page'; // socket连接

  static const IR_START_PATROL = '/ir_start_patrol'; // 测温，开始巡检/结束巡检
  static const IR_UPLOAD_DATA = '/ir_upload_data'; // 测温，上传数据
  static const IR_DEVICE_CHECK = '/ir_device_check'; // 测温，设备检测
  static const IR_TASK = '/phone_ir_task'; //
  static const PHONE_LOGIN = '/phone_login';
  static const PHONE_HOME = '/phone_home';
  static const PHONE_CHEST = '/phone_chest';
  static const PHONE_DEVICES = '/phone_devices';
  static const PHONE_CAMERA = '/phone_camera';
  static const PHONE_PATROL_START = '/phone_patrol_start';
  static const PHONE_PATROL_RECORD = '/phone_patrol_record';

}
