


import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../controllers/global_controller.dart';
import '../controllers/sqflite_controller.dart';

class PatrolRepository {

  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  void checkDeviceWithOutUserId(String tableName , String? userId) async {
     await sqfliteController.checkWithoutDataTableByUserId(tableName, userId);
  }


}