
import "package:flutter/material.dart";
import "package:fluttertoast/fluttertoast.dart";
import "package:get/get.dart";
import "package:sjzx_patrol_system_mobile/app/data/uphold/maintainData.dart";
import "./upholdSqflite_controller.dart";
import "./global_controller.dart";
// 用于socket 接受信息后更新到数据库中，并通知相应的模块进行查库更新视图
class UpholdMessage {
  UpholdSQLController upholdSQLController = new UpholdSQLController();
  GlobalController globalController = Get.find<GlobalController>();

  UpholdMessage(){
    // 初始化的时候需要调用方法创建数据库，以确保数据库被创建和连接
    upholdSQLController.initDatabase();
  }

  /**设备信息*/
  // 该方法接受双方 socket 的信息，插入到本地数据库，根据更新全局中 deviceIndex 的时间戳，通知视图层重新查库更新视图
  deviceInfoFun(data)async{
    // 接受data数据，根据 id 和 stepName字段更新 tabelName 这个属性传过来的表名更新数据
    DateTime d = DateTime.now();
    globalController.setDeviceIndex(d.millisecondsSinceEpoch);
    // 根据用户角色判断更新表中的指定字段 0执行人 1监督人
    if(data['userRole'] == 0){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "operatorId":data['data']['operatorId'],
          "operatorName":data['data']['operatorName'],
          "operatorResult":data['data']['operatorResult'],
          'operatorTime':data['data']['operatorTime']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}'"
      );
    }
    if(data['userRole'] == 1){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "supervisorId":data['data']['supervisorId'],
          "supervisorName":data['data']['supervisorName'],
          "supervisorResult":data['data']['supervisorResult'],
          'supervisorTime':data['data']['supervisorTime']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}'"
      );
    }
  }
  
  /**前提条件 */
  premiseFun(data)async{
    // 根据用户角色判断更新表中的指定字段 0执行人 1监督人
    if(data['userRole'] == 0){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "operatorId":data['data']['operatorId'],
          "operatorName":data['data']['operatorName'],
          "operatorResult":data['data']['operatorResult'],
          'operatorTime':data['data']['operatorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND content='${data['content']}'"
      );
    }
    if(data['userRole'] == 1){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "supervisorId":data['data']['supervisorId'],
          "supervisorName":data['data']['supervisorName'],
          "supervisorResult":data['data']['supervisorResult'],
          'supervisorTime':data['data']['supervisorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND content='${data['content']}'"
      );
    }
    // 接受data数据，根据 id 和 stepName字段更新 tabelName 这个属性传过来的表名更新数据
    DateTime d = DateTime.now();
    globalController.setPremiseIndex(d.millisecondsSinceEpoch);
  }
  
  /**安全保障 */
  guaranteeFun(data)async{
    // 根据用户角色判断更新表中的指定字段 0执行人 1监督人
    if(data['userRole'] == 0){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "operatorId":data['data']['operatorId'],
          "operatorName":data['data']['operatorName'],
          "operatorResult":data['data']['operatorResult'],
          'operatorTime':data['data']['operatorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND content='${data['content']}'" // 查询条件需要加上conten
      );
    }
    if(data['userRole'] == 1){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "supervisorId":data['data']['supervisorId'],
          "supervisorName":data['data']['supervisorName'],
          "supervisorResult":data['data']['supervisorResult'],
          'supervisorTime':data['data']['supervisorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND content='${data['content']}'"
      );
    }
    // 接受data数据，根据 id 和 stepName字段更新 tabelName 这个属性传过来的表名更新数据
    DateTime d = DateTime.now();
    globalController.setGuaranteeIndex(d.millisecondsSinceEpoch);
  }

  /**工具及备件要求 */
  toolFun(data) async{
    // 根据用户角色判断更新表中的指定字段 0执行人 1监督人
    if(data['userRole'] == 0){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "operatorId":data['data']['operatorId'],
          "operatorName":data['data']['operatorName'],
          "operatorResult":data['data']['operatorResult'],
          'operatorTime':data['data']['operatorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND content='${data["content"]}'"
      );
    }
    if(data['userRole'] == 1){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "supervisorId":data['data']['supervisorId'],
          "supervisorName":data['data']['supervisorName'],
          "supervisorResult":data['data']['supervisorResult'],
          'supervisorTime':data['data']['supervisorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND content='${data["content"]}'"
      );
    }
    // 接受data数据，根据 id 和 stepName字段更新 tabelName 这个属性传过来的表名更新数据
    DateTime d = DateTime.now();
    globalController.setToolIndex(d.millisecondsSinceEpoch);
  }

  /**回退计划 */
  recallFun(data)async{
    // 根据用户角色判断更新表中的指定字段 0执行人 1监督人
    if(data['userRole'] == 0){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "operatorId":data['data']['operatorId'],
          "operatorName":data['data']['operatorName'],
          "operatorResult":data['data']['operatorResult'],
          'operatorTime':data['data']['operatorTime']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}'"
      );
    }
    if(data['userRole'] == 1){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "supervisorId":data['data']['supervisorId'],
          "supervisorName":data['data']['supervisorName'],
          "supervisorResult":data['data']['supervisorResult'],
          'supervisorTime':data['data']['supervisorTime']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}'"
      );
    }
    // 接受data数据，根据 id 和 stepName字段更新 tabelName 这个属性传过来的表名更新数据
    DateTime d = DateTime.now();
    globalController.setRecallIndex(d.millisecondsSinceEpoch);
  }

  /**操作流程 */
  processFun(data)async{
    // 根据用户角色判断更新表中的指定字段 0执行人 1监督人
    if(data['userRole'] == 0){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "operatorId":data['data']['operatorId'],
          "operatorName":data['data']['operatorName'],
          "operatorResult":data['data']['operatorResult'],
          'operatorTime':data['data']['operatorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND parentName='${data["parentName"]}' AND content='${data["content"]}'"
      );
    }
    if(data['userRole'] == 1){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "supervisorId":data['data']['supervisorId'],
          "supervisorName":data['data']['supervisorName'],
          "supervisorResult":data['data']['supervisorResult'],
          'supervisorTime':data['data']['supervisorTime'],
          'gapPrice':data['data']['gapPrice']
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}' AND parentName='${data["parentName"]}' AND content='${data["content"]}'"
      );
    }
    // 接受data数据，根据 id 和 stepName字段更新 tabelName 这个属性传过来的表名更新数据
    DateTime d = DateTime.now();
    globalController.setProcessIndex(d.millisecondsSinceEpoch);
  }



  /**签字 */
  signFun(data)async{
    // 根据用户角色判断更新表中的指定字段 0执行人 1监督人
    if(data['userRole'] == 0){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "operateSignature":data["data"]["operateSignature"],
          "operateTime":data["data"]["operateTime"]
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}'"
      );
      toastFun("终端签名已同步");
    }
    if(data['userRole'] == 1){
      upholdSQLController.updateTable(
        data['tabelName'], 
        {
          "supervisorSignature":data["data"]["supervisorSignature"],
          "supervisorTime":data["data"]["supervisorTime"]
        }, 
        "woid='${data['id']}' AND stepName='${data['stepName']}'"
      );
      toastFun("终端签名已同步");
    }
  }





  /**交互优化*/
  toastFun(toastText){
    Fluttertoast.showToast(
      msg: "$toastText",
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.CENTER,
      timeInSecForIosWeb: 2,
      backgroundColor: Colors.black,
      textColor: Colors.white,
      fontSize: 16.0
    );
  }



}