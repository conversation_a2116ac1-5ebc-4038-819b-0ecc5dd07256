import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import 'package:qr_flutter/qr_flutter.dart';
import './upholdMessage.dart';
import "./global_controller.dart";

class SocketServerController extends GetxController {
  GlobalController globalController = Get.find<GlobalController>();
  late ServerSocket? serverSocket = null;
  late Socket? socketParams = null;
  RxString sniff = "".obs; // 用于接受服务端的嗅探信息
  late UpholdMessage upholdMessage;

  @override
  void onInit() {
    super.onInit();
    upholdMessage = UpholdMessage();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  createSocket(IP,_port)async{
    try {
      serverSocket = await ServerSocket.bind(IP, _port);
      print("socket服务器创建成功,当前网络IP为 ${IP}");
      serverInfoAlter(IP);
      serverSocket?.listen(handelSocket);
    } catch (e) {
      print(e.toString());
    }
  }

  // 监听socket
  handelSocket(Socket  socket){
    // 只有接收到客户端的消息后才会进行初始化
    socketParams = socket;
    // 向服务端发送嗅探消息
    var canaryContent = {"canary":"hello user"};
    var canaryJson =  jsonEncode(canaryContent);
    socket.write(utf8.encode(canaryJson));

    socket.listen((data) async {
      var str = utf8.decode(data);
      List<dynamic> jsonList = jsonDecode(str);
      List<int> intList = List<int>.from(jsonList.map((item) => item as int));
      Uint8List bytes = Uint8List.fromList(intList);
      var jsonMap = json.decode(utf8.decode(bytes));
      print('接受客户端信息: ${jsonMap}');
      if(jsonMap['canary'] == "hello server"){ // 嗅探信息提示弹窗双向通信成功
        sniff.value = jsonMap['canary'];
        globalController.socketStatus.value = "socket已连接";
        globalController.socketType.value = 1;
      }else{
        // 判断模块调用不同的方法将收到的信息更新本地库
        if(jsonMap['stepName'] == "设备信息"){
          await upholdMessage.deviceInfoFun(jsonMap);
        }
        if(jsonMap['stepName'] == "先提条件"){
          // 签字操作
          // if(jsonMap['tabelName']=="stepTake"){
          //   await upholdMessage.signFun(jsonMap);
          //   return;
          // }
          await upholdMessage.premiseFun(jsonMap);
        }
        if(jsonMap['stepName'] == "安全保障"){
          await upholdMessage.guaranteeFun(jsonMap);
        }
        if(jsonMap['stepName'] == "工具及备件要求"){
          await upholdMessage.toolFun(jsonMap);
        }
        if(jsonMap['stepName'] == "回退计划"){
          await upholdMessage.recallFun(jsonMap);
        }
        if(jsonMap['stepName'] == "操作流程"){
          await upholdMessage.processFun(jsonMap);
        }
      }
    }).onDone(() {
      Fluttertoast.showToast(
        msg: "检测到当前socket已断开请重新连接",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 5,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
      globalController.socketStatus.value = "socket已断开";
      globalController.socketType.value = 0;
      sniff.value = "";
      closeSocket();
    });
  }

  // 发送
  void send(massage) async {
    if(socketParams != null){
      socketParams?.write(massage);
    }else{
      print("socketParams尚未初始化");
    }
  }

  // 监听关闭
  void closeSocket() {
    // 需要线关闭监听的_serverSocketObj
    if (serverSocket != null) {
      serverSocket?.close();
      serverSocket = null;
      sniff.value = "";
      print('Server Socket closed');
    }
  }

  // 弹窗当前IP地址
  serverInfoAlter(IP){
    Get.dialog(
      barrierDismissible:false,
      AlertDialog(
        content:Obx(()=>Container(
          width: MyScreenUtil.width(400),
          height: MyScreenUtil.height(400),
          child: Column(
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child:Row(
                  children: [
                    Container(
                      margin: EdgeInsets.only(right: MyScreenUtil.width(10)),
                      child: Icon(
                        Icons.error,
                        size: MyScreenUtil.fontSize(26),
                      ),
                    ),
                    Text(
                      "提示信息",
                      style:TextStyle(
                        fontSize: MyScreenUtil.fontSize(24),
                        fontWeight: FontWeight.w500
                      ),
                    )
                  ],
                )
              ),
              // 根据 IP 创建二维码, 供客户端扫码连接
              // QrImage(
              //   data: "$IP",
              //   version: QrVersions.auto,
              //   size: MyScreenUtil.width(200),
              // ),
              Container(
                margin: EdgeInsets.only(top: MyScreenUtil.height(5)),
                child: Text(
                  "${IP}",
                  style: TextStyle(
                    fontSize: MyScreenUtil.fontSize(18)
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: MyScreenUtil.height(5),bottom: MyScreenUtil.height(20)),
                child: sniff.value != ""?Text(
                  "客户端连接成功,监听到了来自客户端的嗅探信息",
                  style: TextStyle(
                    fontSize: MyScreenUtil.fontSize(20)
                  ),
                ) : Text(
                  "等待客户端连接...",
                  style: TextStyle(
                    fontSize: MyScreenUtil.fontSize(20)
                  ),
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                child: ElevatedButton(
                  onPressed: (){
                    Get.back();
                  }, 
                  child:Text(
                    "关闭弹窗",
                    style: TextStyle(
                      fontSize: MyScreenUtil.fontSize(18)
                    ),
                  )
                ),
              )
            ],
          ),
        ))
      )
    );
  }



}
