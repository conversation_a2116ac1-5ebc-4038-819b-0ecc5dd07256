import 'dart:ffi';

import 'package:get/get.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

import '../data/uphold/history.dart';
import '../data/uphold/user.dart';
import "../data/uphold/workOrder.dart";
import "../data/uphold/stepTakeData.dart";
import "../data/uphold/maintainData.dart";

class UpholdSQLController extends GetxController {
  late Database _database;

  @override
  void onInit() async {
    print('维护工单数据库初始化');
    super.onInit();
    await initDatabase();
  }

  initDatabase() async {
    print("被调用了");
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, 'upholdDB.db');
    _database =
        await openDatabase(path, version: 1, onCreate: (db, version) async {
      /**
         * 创建 workOrder 维护工单列表
         */
      await db.execute('''
          CREATE TABLE  workOrder (
            id TEXT PRIMARY KEY, -- 工单ID
            maintenancePlanNum TEXT NULL, -- 维护工单编号
            deptId TEXT NULL, -- 部门ID
            deptName TEXT NULL, --部门名称
            deviceGroupId TEXT NULL, --设备组ID
            deviceGroupName TEXT NULL, --设备组名称
            type INTEGER NULL, --维护类型code
            typeName TEXT NULL, --维护类型名称
            installationSite TEXT NULL, --设备位置
            model TEXT NULL, --?
            manufacturer TEXT NULL, -- 生产厂家
            factory TEXT NULL, --厂家
            deviceFlowId TEXT NULL, --设备流ID
            deviceNum TEXT NULL, -- 设备编号
            phone TEXT NULL, --电话
            contactPhone TEXT NULL, -- 联系人电话
            planTime INTEGER NULL, --计划维护时间
            isFinish INTEGER, -- (自定义字段)工单是否完成,未完成0,已完成1
            isUpload INTEGER, -- (自定义字段)工单是否上传,未上传0,已上传1
            isPreview INTEGER, -- (自定义字段)是否预览,未预览0,已预览1
            finishDate INTEGER, -- (自定义字段-时间戳)完成时间
            finishStep INTEGER, --(自定义字段-完成步骤)
            week TEXT, -- 周
            parentGroupId TEXT, --专业ID
            parentGroupName TEXT, --专业名称
            userId TEXT NULL-- userId
          );
        ''');

      /**创建 user表，根据工单id和自定义身份id */
      await db.execute('''
          CREATE TABLE  user (
            woid TEXT NULL, -- 工单id
            uid TEXT NULL, -- 用户id
            name TEXT NULL, -- 用户姓名
            gender INTEGER NULL, -- 性别 1男生 0女生
            phone TEXT NULL, -- 手机
            status INTEGER, -- 状态
            role INTEGER -- (自定义字段)角色 0执行人 1监督人 2审核人
          );
        ''');
      /**创建 stepTake 步骤记录存放维护签字时间等  */
      await db.execute('''
          CREATE TABLE  stepTake (
            stepName TEXT, --步骤名称(自定义字段)
            stepField TEXT, -- 步骤的接口字段(自定义字段)
            woid TEXT NULL, -- 工单id
            id TEXT NULL, -- 步骤id
            operateTime INTEGER NULL, -- 操作时间
            operateSignature TEXT NULL, -- 操作签名
            supervisorTime INTEGER NULL, -- 监管时间
            supervisorSignature TEXT NULL -- 监管签名
          );
        ''');

      /**创建 maintainItem 存放设备维护项*/
      await db.execute('''
          CREATE TABLE  maintainItem (  
            stepName TEXT NULL, --父级步骤名称
            woid TEXT NULL, -- 工单id 和 workOrder表中关联
            id TEXT NULL, -- 步骤id 和 stepTake表中关联
            parentName TEXT NULL, -- 父级名称（自定义字段，对于操作流程项目存在子集项目，根据此字段分组）
            parentId TEXT NULL, -- 父级Id（自定义字段，对于操作流程项目存在子集项目，根据此字段分组）
            content TEXT NULL, -- 内容
            operation TEXT NULL, -- 操作
            operatorId TEXT NULL, -- 执行人员ID
            operatorName TEXT NULL, -- 执行人员姓名
            operatorResult INTEGER NULL, -- 操作结果 1表示已执行 0表示未执行
            operatorTime INTEGER NULL, -- 操作时间
            supervisorId TEXT NULL, -- 监管人员ID
            supervisorName TEXT NULL, -- 监管人员姓名
            supervisorResult INTEGER NULL, -- 监管结果 1表示已执行 0表示未执行
            supervisorTime INTEGER NULL, -- 监管时间
            fillingValue INTEGER NULL, -- 维护项是否填值 0表示不要 1表示要
            amount INTEGER NULL, --维护项填值的个数
            gapPrice TEXT NULL, -- 维护项填值的list, 前端存储为字符串
            status INTEGER, -- 判断当前项是否执行 0未执行  1已执行
            gapPicture TEXT NULL, -- 维护项上传图片
            fillingPictureValue INTEGER NULL -- 维护项是否需要上传图片
          );
        ''');

      /**创建 history 表存放维护工单上传后的记录*/
      await db.execute('''
          CREATE TABLE  history (
            id TEXT PRIMARY KEY, -- 工单ID
            deptId TEXT NULL, -- 部门ID
            deptName TEXT NULL, --部门名称
            deviceGroupId TEXT NULL, --设备组ID
            deviceGroupName TEXT NULL, --设备组名称
            type INTEGER NULL, --维护类型code
            typeName TEXT NULL, --维护类型名称
            installationSite TEXT NULL, --设备位置
            operator TEXT NULL, --执行人
            supervision TEXT NULL, --监督人
            pe TEXT NULL, -- 审核人
            manufacturer TEXT NULL, -- 生产厂家
            factory TEXT NULL, --厂家
            deviceFlowId TEXT NULL, --设备流ID
            deviceNum TEXT NULL, -- 设备编号
            phone TEXT NULL, --电话
            contactPhone TEXT NULL, -- 联系人电话
            planTime TEXT NULL, --计划维护时间
            week TEXT NULL, -- 周
            maintenancePlanNum TEXT NULL -- 维护工单编号
          );
        ''');
    });
  }

  /**工单列表 workOrder表的操作 */
  Future<void> insertWorkOrder(List<WorkOrder> workOrders, userId) async {
    final db = await _database;
    final batch = db.batch();
    for (final workOrderItem in workOrders) {
      workOrderItem.userId = userId;
      batch.insert(
        'workOrder',
        workOrderItem.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  // 查询改查询数据是否为空
  Future<bool> isExistData(String tableName , String? userId) async {
    if(userId == null) return false;
    final db = await _database;
    var l = await db.rawQuery("SELECT * FROM $tableName WHERE userId='$userId'");
    return l.isNotEmpty;
  }


  // 查询改查询数据是否为空
  Future<bool> isExistDataItem(String tableName ,String? id, String? userId) async {
    if(userId == null) return false;
    final db = await _database;
    var l = await db.rawQuery("SELECT * FROM $tableName WHERE id='$id' AND userId='$userId'");
    return l.isNotEmpty;
  }

  Future<List<WorkOrder>> findWorkOrder(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps;
    if (sql == "") {
      maps = await db.query('workOrder');
    } else {
      // maps = await db.query('workOrder',where:sql,);
      maps = await db.query(
        'workOrder',
        where: sql,
        orderBy: 'isFinish DESC', // 按照 isFinish 倒序排序
      );
    }
    return List.generate(maps.length, (i) {
      return WorkOrder(
        id: maps[i]["id"],
        maintenancePlanNum:maps[i]['maintenancePlanNum'],
        deptId: maps[i]["deptId"],
        deptName: maps[i]["deptName"],
        deviceGroupId: maps[i]["deviceGroupId"],
        deviceGroupName: maps[i]["deviceGroupName"],
        type: maps[i]["type"],
        typeName: maps[i]["typeName"],
        installationSite: maps[i]["installationSite"],
        model: maps[i]["model"],
        manufacturer: maps[i]["manufacturer"],
        deviceNum: maps[i]["deviceNum"],
        phone: maps[i]["phone"],
        contactPhone: maps[i]["contactPhone"],
        factory: maps[i]["factory"],
        deviceFlowId: maps[i]["deviceFlowId"],
        planTime: maps[i]["planTime"],
        isFinish: maps[i]["isFinish"],
        isUpload: maps[i]["isUpload"],
        isPreview: maps[i]["isPreview"],
        finishStep: maps[i]["finishStep"],
        finishDate: maps[i]["finishDate"],
        week: maps[i]["week"],
        parentGroupId:maps[i]['parentGroupId'],
        parentGroupName:maps[i]['parentGroupName'],
        userId:maps[i]['userId'],
      );
    });
  }

  Future<List<WorkOrder>> findWork(sql, args) async {
    print(sql);
    // return;
    final db = await _database;
    final List<Map<String, dynamic>> maps;
    if (sql == "") {
      maps = await db.query('workOrder');
    } else {
      // maps = await db.query('workOrder',where:sql,);
      if (args.length < 1) {
        maps = await db.rawQuery(sql);
      }else{
        maps = await db.rawQuery(sql, args);

      }
    }
    return List.generate(maps.length, (i) {
      return WorkOrder(
        id: maps[i]["id"],
        maintenancePlanNum:maps[i]['maintenancePlanNum'],
        deptId: maps[i]["deptId"],
        deptName: maps[i]["deptName"],
        deviceGroupId: maps[i]["deviceGroupId"],
        deviceGroupName: maps[i]["deviceGroupName"],
        type: maps[i]["type"],
        typeName: maps[i]["typeName"],
        installationSite: maps[i]["installationSite"],
        model: maps[i]["model"],
        manufacturer: maps[i]["manufacturer"],
        deviceNum: maps[i]["deviceNum"],
        phone: maps[i]["phone"],
        contactPhone: maps[i]["contactPhone"],
        factory: maps[i]["factory"],
        deviceFlowId: maps[i]["deviceFlowId"],
        planTime: maps[i]["planTime"],
        isFinish: maps[i]["isFinish"],
        isUpload: maps[i]["isUpload"],
        isPreview: maps[i]["isPreview"],
        finishStep: maps[i]["finishStep"],
        finishDate: maps[i]["finishDate"],
        week: maps[i]["week"],
        parentGroupId:maps[i]["parentGroupId"],
        parentGroupName:maps[i]["parentGroupName"]
      );
    });
  }

  /**用户表 user 操作 */
  Future<void> insertUser(List<User> users) async {
    final db = await _database;
    final batch = db.batch();
    for (final userItem in users) {
      batch.insert(
        'user',
        userItem.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  Future<List<User>> findUser(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query('user', where: sql);
    return List.generate(maps.length, (i) {
      return User(
          woid: maps[i]["woid"],
          uid: maps[i]["uid"],
          name: maps[i]["name"],
          gender: maps[i]["gender"],
          phone: maps[i]["phone"],
          status: maps[i]["status"],
          role: maps[i]["role"]);
    });
  }

  /*步骤表 stepTake 操作*/
  Future<void> insertStepTake(List<StepTakeData> stepTakes) async {
    final db = await _database;
    final batch = db.batch();
    for (final stepTakeItem in stepTakes) {
      batch.insert(
        'stepTake',
        stepTakeItem.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  Future<List<StepTakeData>> findStepTake(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps =
        await db.query('stepTake', where: sql);
    return List.generate(maps.length, (i) {
      return StepTakeData(
        stepName: maps[i]["stepName"],
        stepField: maps[i]["stepField"],
        woid: maps[i]["woid"],
        id: maps[i]["id"],
        operateTime: maps[i]["operateTime"],
        operateSignature: maps[i]["operateSignature"],
        supervisorTime: maps[i]["supervisorTime"],
        supervisorSignature: maps[i]["supervisorSignature"],
      );
    });
  }

  /**维护项 maintainItem 操作 */
  Future<void> insertMaintainItem(List<MaintainData> maintains) async {
    final db = await _database;
    final batch = db.batch();
    for (final maintainItem in maintains) {
      batch.insert(
        'maintainItem',
        maintainItem.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  Future<List<MaintainData>> findMaintainItem(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps =
        await db.query('maintainItem', where: sql);
    return List.generate(maps.length, (i) {
      return MaintainData(
        woid: maps[i]["woid"],
        stepName: maps[i]["stepName"],
        id: maps[i]["id"],
        content: maps[i]["content"],
        operation: maps[i]["operation"],
        operatorId: maps[i]["operatorId"],
        operatorName: maps[i]["operatorName"],
        parentId: maps[i]["parentId"],
        operatorResult: maps[i]["operatorResult"],
        operatorTime: maps[i]["operatorTime"],
        supervisorId: maps[i]["supervisorId"],
        supervisorName: maps[i]["supervisorName"],
        supervisorResult: maps[i]["supervisorResult"],
        supervisorTime: maps[i]["supervisorTime"],
        parentName: maps[i]["parentName"],
        fillingValue: maps[i]["fillingValue"],
        amount: maps[i]["amount"],
        gapPrice: maps[i]["gapPrice"],
        status: maps[i]['status'],
        gapPicture: maps[i]['gapPicture'],
        fillingPictureValue: maps[i]['fillingPictureValue'],
      );
    });
  }

  /**维护记录 history 操作 */
  Future<void> insertHistory(List<UpholdHistory> upholdHistory) async {
    final db = await _database;
    final batch = db.batch();
    for (final upholdHistoryItem in upholdHistory) {
      batch.insert(
        'history',
        upholdHistoryItem.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    await batch.commit();
  }

  Future<List<UpholdHistory>> findHistory(sql) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps;
    if (sql == "") {
      maps = await db.query('history');
    } else {
      maps = await db.query('history', where: sql);
    }
    return List.generate(maps.length, (i) {
      return UpholdHistory(
          id: maps[i]["id"],
          deptId: maps[i]["deptId"],
          deptName: maps[i]["deptName"],
          deviceGroupId: maps[i]["deviceGroupId"],
          deviceGroupName: maps[i]["deviceGroupName"],
          type: maps[i]["type"],
          typeName: maps[i]["typeName"],
          installationSite: maps[i]["installationSite"],
          operator: maps[i]["operator"],
          supervision: maps[i]["supervision"],
          pe: maps[i]["pe"],
          manufacturer: maps[i]["manufacturer"],
          factory: maps[i]["factory"],
          deviceFlowId: maps[i]["deviceFlowId"],
          deviceNum: maps[i]["deviceNum"],
          phone: maps[i]["phone"],
          contactPhone: maps[i]["contactPhone"],
          planTime: maps[i]["planTime"],
          week:maps[i]["week"],
          maintenancePlanNum:maps[i]["maintenancePlanNum"]);
    });
  }

  // 更新数据
  Future<void> updateTable(
      String tableName, Map<String, dynamic> params, String sql) async {
    final db = await _database;
    if (sql == "") {
      await db.update(tableName, params);
    } else {
      await db.update(
        tableName,
        params,
        where: sql,
      );
    }
  }

  // 清空数据
  Future<void> clearTable(String tableName) async {
    final db = await _database;
    await db.rawDelete('DELETE FROM $tableName');
  }

  // 清空数据
  Future<void> clearTableByUserId(String tableName , String? userId) async {
    if(userId == null) return;
    final db = await _database;
    await db.rawDelete("DELETE FROM $tableName WHERE userId='$userId'");
  }

  // 清空数据
  Future<void> clearTableByOrderId(String tableName , woid) async {
    if(woid == null) return;
    final db = await _database;
    await db.rawDelete("DELETE FROM $tableName WHERE woid='$woid'");
  }


  // 查询表是否存在
  Future<bool> isExist(String tableName) async {
    final db = await _database;
    var result = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'");
    return result.isNotEmpty;
  }
}
