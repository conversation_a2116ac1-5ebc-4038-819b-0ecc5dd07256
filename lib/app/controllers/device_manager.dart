
import 'package:sjzx_patrol_system_mobile/app/api/env_config.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';

import '../routes/app_pages.dart';
import '../utils/storage.dart';

class DeviceManager {

  static Future<String> initRoutePath() async {
    var isPad = await DeviceConfig.isPad();
    var isLogin = await Storage.getData("userInfo");
    var initRoute = Routes.HOME;
    if(isLogin != null){
      if(isPad){
        initRoute = Routes.HOME;
      }else{
        initRoute = Routes.PHONE_HOME;
      }
    }else {
      if(!isPad){
        initRoute = Routes.PHONE_LOGIN;
      }else {
        initRoute = Routes.LOGIN;
      }
    }
    return initRoute;

  }

}