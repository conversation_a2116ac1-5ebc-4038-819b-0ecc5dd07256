import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

/// 记录上次登录的手机号
var SHAREPREFERENCE_LAST_LOGINED_MOBILE = "SHAREPREFERENCE_LAST_LOGINED_MOBILE";


class Storage{
  static setData(String key,dynamic value) async{
      var prefs=await SharedPreferences.getInstance();
      prefs.setString(key, json.encode(value));
  }
  static getData(String key) async{
      var prefs=await SharedPreferences.getInstance();
      String? tempData=prefs.getString(key);
      if(tempData == null){
        return null;
      }else{
        return json.decode(tempData!);
      }
  }
  static removeData(String key) async{
      var prefs=await SharedPreferences.getInstance();
      prefs.remove(key);    
  }



  static setConfig(String key, String value) async{
    var prefs=await SharedPreferences.getInstance();
    prefs.setString(key, value);
  }
  static Future getConfig(String key) async{
    var prefs = await SharedPreferences.getInstance();
    String? tempData=prefs.getString(key);
    return tempData;
  }

}