// dio 二次封装

import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import "../controllers/global_controller.dart";

import "package:get/get.dart";

import 'logger_interceptor.dart';

class HttpsClient{
  // static String baseUrl = "https://jdmall.itying.com/"; 
  // static String baseUrl = "http://************:9613/"; // 测试环境
  // static String baseUrl = "http://************:8080/"; // 公网ip环境
  // static String baseUrl = "http://**********:80/"; // 正式环境
  static Dio dio = Dio();
  

  HttpsClient(){
    // dio.options.baseUrl = baseUrl;
    dio.options.connectTimeout = const Duration(seconds: 120); // 连接超时 秒s
    dio.options.receiveTimeout = const Duration(seconds: 120); // 接收数据超时 单位秒
    dio.interceptors.add(CustomInterceptors()); // dio 拦截器
    // dio.interceptors.add(LinqDioLogger(enableLog: true));
    // (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
    //      client.findProxy = (uri) {
    //        return 'PROXY **********:8888';
    //      };
    // };
  }


  Future get(apiUrl,[queryParameters]) async {
    try {
      var response = await dio.get(apiUrl,queryParameters:queryParameters);
      return response;
    } catch (e) {
      Fluttertoast.showToast(
        msg: "网络开小差",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 2,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
      return null;
    }
  }

  Future post(apiUrl,data) async {
    try {
      var response = await dio.post(apiUrl,data:data);
      return response;
    } catch (e) {
      Fluttertoast.showToast(
        msg: "网络开小差",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 2,
        backgroundColor: Colors.black,
        textColor: Colors.white,
        fontSize: 16.0
      );
      return null;
    }
  }
}

// 定义拦截器类
class CustomInterceptors extends Interceptor {
  GlobalController globalController = Get.find();
  // 请求拦截器
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // 检查是否登录
    final isAuthenticated = checkIfAuthenticated();
    if (options.path != "/employee/info/v1") {

      // 添加 token
      final token = getToken();
      options.headers['token'] = token;
    }
    // print('REQUEST[${options.method}] => PATH: ${options.path}');
    super.onRequest(options, handler);
    
  }

  // 响应拦截器
  @override
  void onResponse(dynamic response, ResponseInterceptorHandler handler) {
    // print('RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}');
    super.onResponse(response, handler);
    if (response.statusCode == 401) { // token失效或用户未登录
      // 跳转到登录页面
      // Navigator.pushNamedAndRemoveUntil(context, '/login', (_) => false);
      Get.offAllNamed("/login");
    }

  }

  // 检查用户是否登录
  checkIfAuthenticated(){
    return false;
  }

  // 获取用户 token
  getToken(){
    var token = globalController.userInfo.value?.data.token??"";
    return token;
  }

}
