import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';


showSimpleDialog(BuildContext context,
    {String? title,
      String? cancelText,
      String? confirmText,
      Function? cancelCallBack,
      Function? confirmCallBack,
      bool? touchDismiss}) {
  showDialog(
      context: context,
      barrierDismissible: touchDismiss ?? true,
      builder: (context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '温馨提示',
                style: const TextStyle(fontSize: 16, color: Colors.blue),
              ),
              10.gap,
              Text(
                (title ?? ''),
                style: const TextStyle(fontSize: 19),
              )
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                cancelText ?? '取消',
                style: TextStyle(fontSize: 19),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (cancelCallBack != null) {
                  cancelCallBack();
                }
              },
            ),
            TextButton(
              child: Text(
                confirmText ?? '确认',
                style: const TextStyle(fontSize: 19),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (confirmCallBack != null) {
                  confirmCallBack();
                }
              },
            ),
          ],

        );
      });
}


showPermissionDialog(
  BuildContext context, {
  String? title,
  String? cancelText,
  String? confirmText,
  Function? cancelCallBack,
  Function? confirmCallBack,
  required VoidCallback? checkboxValue(bool),
}) {
  showDialog(
      context: context,
      builder: (context) {
        bool _checkbox = false;
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                "温馨提示",
                style: const TextStyle(fontSize: 19, color: Colors.blue),
              ),
              10.gap,
              Text(
                (title ?? ''),
                style: const TextStyle(fontSize: 16),
              ),
              10.gap,
              Row(
                children: [
                  Text(
                    '不再询问',
                  ),
                  StatefulBuilder(builder: (context, _setState) {
                    return Checkbox(
                        value: _checkbox,
                        onChanged: (value) {
                          print('${value}');
                          _setState(() {
                            _checkbox = !_checkbox;
                            checkboxValue.call(_checkbox);
                          });
                        });
                  }),
                ],
              )
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                cancelText ?? '取消',
                style: TextStyle(fontSize: 19),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (cancelCallBack != null) {
                  cancelCallBack();
                }
              },
            ),
            TextButton(
              child: Text(
                confirmText ?? "确认",
                style: const TextStyle(fontSize: 19),
              ),
              onPressed: () {
                Navigator.of(context).pop();
                if (confirmCallBack != null) {
                  confirmCallBack();
                }
              },
            ),
          ],
        );
      });
}


