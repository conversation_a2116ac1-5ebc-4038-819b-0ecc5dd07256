import 'dart:async';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/permission/permission_collection_dialog.dart';


class PermissionUtil {

  static Future<PermissionStatus> requestContactsPermissionStatus() async{
    return await Permission.contacts.status;
  }

  static Future<Map<Permission, PermissionStatus>> requestCameraAndPhotoPermission() async{
    Map<Permission, PermissionStatus> statuses = await [
      Permission.camera,
      Permission.storage,
    ].request();
    return statuses;
  }

  static Future<Map<Permission, PermissionStatus>> requestPhotoPermission() async{
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
    ].request();
    return statuses;
  }

  static Future<Map<Permission, PermissionStatus>> requestBasePermission() async{
    Map<Permission, PermissionStatus> statuses = await [
      Permission.phone,
      Permission.storage,
    ].request();
    return statuses;
  }

  static Future<Map<Permission, PermissionStatus>> requestLocationPermission() async{
    Map<Permission, PermissionStatus> statuses = await [
      Permission.locationWhenInUse,
    ].request();
    return statuses;
  }


  // 图片上传申请权限
  static showPhotoPrivacyDialog(BuildContext context , {String? tip , Function? callback}) {
    showSimpleDialog(context , title: tip ?? "需要您授权使用存储卡权限才能进行图片上传等操作", cancelText: '拒绝', confirmText: '允许', confirmCallBack: (){
      if(callback != null){
        callback();
      }
    });
  }

  // 图片上传申请权限
  static showBasePermissionDialog(BuildContext context , {Function? callback}) {
    showSimpleDialog(context , title: "需要您授权使用存储卡等权限才能正常使用担当app", cancelText: '拒绝', confirmText: '允许', confirmCallBack: (){
      if(callback != null){
        callback();
      }
    });
  }

  static showCameraPrivacyDialog(BuildContext context , {Function? callback}) {
    showSimpleDialog(context , title: '需要您授权使用拍照权限才能进行图片上传', cancelText: '拒绝', confirmText: '允许', confirmCallBack: (){
      if(callback != null){
        callback();
      }
    });
  }

  static showLocationPrivacyDialog(BuildContext context , {Function? callback}) {
    showSimpleDialog(context , title: '位置权限', cancelText: '拒绝', confirmText: '允许', confirmCallBack: (){
      if(callback != null){
        callback();
      }
    });
  }


  // 为方便将camera和photo权限一起申请
  static Future<bool> checkPhotoPermission(BuildContext context) async {
    Completer<bool> c = Completer();
    if(Platform.isAndroid){

      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo = await deviceInfoPlugin.androidInfo;

      bool isDenied = false;
      if((deviceInfo.version.sdkInt ?? 30) >= 33) {
        var value = await Future.wait([Permission.camera.status]);
        isDenied = (value[0] == PermissionStatus.denied);
      }else {
        var value = await Future.wait([Permission.camera.status,Permission.storage.status]);
        isDenied = (value[0] == PermissionStatus.denied || value[1] == PermissionStatus.denied);
      }

      if(isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(context , callback: (){
          PermissionUtil.requestPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if(value.isDenied){
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      }else {
        c.complete(true);
      }
    }else{
      c.complete(true);
    }
    return c.future;
  }


  static Future<bool> checkStoragePermission(BuildContext context) async {
    Completer<bool> c = Completer();
    if(Platform.isAndroid){
      var value = await Future.wait([Permission.storage.status]);
      if(value[0] == PermissionStatus.denied) {
        PermissionUtil.showBasePermissionDialog(context , callback: (){
          PermissionUtil.requestBasePermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if(value.isDenied){
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      }else {
        c.complete(true);
      }
    }else{
      c.complete(true);
    }
    return c.future;
  }

  static Future<bool> checkCameraPermission(BuildContext context , {String? tip}) async {
    Completer<bool> c = Completer();
    if(Platform.isAndroid){

      final deviceInfoPlugin = DeviceInfoPlugin();
      final deviceInfo = await deviceInfoPlugin.androidInfo;

      bool isDenied = false;
      if((deviceInfo.version.sdkInt ?? 30) >= 33) {
        var value = await Future.wait([Permission.camera.status]);
        isDenied = (value[0] == PermissionStatus.denied);
      }else {
        var value = await Future.wait([Permission.camera.status,Permission.storage.status]);
        isDenied = (value[0] == PermissionStatus.denied || value[1] == PermissionStatus.denied);
      }

      if(isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(context , tip: tip , callback: (){
          PermissionUtil.requestCameraAndPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if(value.isDenied){
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      }else {
        c.complete(true);
      }
    }else{
      c.complete(true);
    }
    return c.future;
  }

  static checkGalleryPermission(BuildContext context , {String? tip}) async {
    Completer<bool> c = Completer();
    if(Platform.isAndroid){

      bool isDenied = false;

      var value = await Future.wait([Permission.storage.status]);
      isDenied = (value[0] == PermissionStatus.denied);

      if(isDenied) {
        PermissionUtil.showPhotoPrivacyDialog(context , tip: tip, callback: (){
          PermissionUtil.requestPhotoPermission().then((photoP) {
            var _grant = true;
            photoP.forEach((key, value) {
              if(value.isDenied){
                _grant = false;
                c.complete(_grant);
              }
            });
            c.complete(_grant);
          });
        });
      }else {
        c.complete(true);
      }
    }else{
      c.complete(true);
    }
    return c.future;
  }
}


var scanPermissionTip = "需要您授权使用存储卡，相机等权限才能进行设备温度获取";
var takePhotoPermissionTip = "需要您授权使用存储卡才能进行上传图片等操作";
var cameraPhotoPermissionTip = "需要您授权使用存储卡，相机等权限才能进行拍照上传等操作";