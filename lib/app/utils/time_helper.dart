

/// 时间戳 转 2024-02-23 08：50：31
class TimeHelper {
    static String timeStamp2FullFormat(int t) {
      DateTime date = DateTime.fromMillisecondsSinceEpoch(t);
      if(t <=0 )return "";
      var month = date.month < 10 ? "0${date.month}" : "${date.month}";
      var day = date.day < 10 ? "0${date.day}" : "${date.day}";
      var hour = date.hour < 10 ? "0${date.hour}" : "${date.hour}";
      var minute = date.minute < 10 ? "0${date.minute}" : "${date.minute}";
      var second = date.second < 10 ? "0${date.second}" : "${date.second}";
      return "${date.year}-${month}-${day} ${hour}:${minute}:${second}";
    }
    // 将秒数转换为时分秒格式
    static String formatDuration(int seconds) {
      if (seconds <= 0) return "0秒";
      int hours = seconds ~/ 3600;
      int minutes = (seconds % 3600) ~/ 60;
      int remainingSeconds = seconds % 60;
      String result = "";
      if (hours > 0) {
        result += "$hours小时";
      }
      if (minutes > 0) {
        result += "$minutes分钟";
      }
      if (remainingSeconds > 0 || result.isEmpty) {
        result += "$remainingSeconds秒";
      }
      return result;
    }

}