

import 'dart:async';

import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir_task/ir_task.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/future_ex.dart';

import 'app_database.dart';
import 'db_constant.dart';
import 'ir/ir_chest.dart';
import 'ir/ir_chest_state.dart';

final appDatabase = $FloorAppDataBase.databaseBuilder('$APP_DATABASE_NAME').build();


class DBHelper {

  // ****************************** 任务 ******************************
  // 【任务】获取所有
  static Future<List<IrTask>> getAllTasks(String userId) async {
    final db = await appDatabase;
    var tasks = await db.getTaskDao.findAllTasks(userId);
    return tasks;
  }

  // 【任务】 删除全部任务
  static Future<void> clearTasks(String userId) async {
    final db = await appDatabase;
    return await db.getTaskDao.clear(userId);
  }

  // 【任务】 监听任务数据变化
  static Stream<List<IrTask>> listenTaskList(String userId) async* {
    final db = await appDatabase;
    yield* db.getTaskDao.listenChestList(userId).map((event) {
      return event;
    });
  }

  // 【任务】 更新任务
  static Future<void> insertTaskList(List<IrTask> list) async {
    final db = await appDatabase;
    return await db.getTaskDao.insertList(list);
  }

  //  更新已检测设备数据
  static Future<void> updateCheckDeviceCount(String userId,String taskId, int checkedDeviceCount) async {
    final db = await appDatabase;
    return await db.getTaskDao.updateCheckedDeviceCount(userId , taskId ,checkedDeviceCount);
  }

  static Future<void> startCheck(String userId,String taskId, int t) async {
    final db = await appDatabase;
    return await db.getTaskDao.startCheck(userId , taskId ,t);
  }

  static Future<void> endCheck(String userId,String taskId, int t) async {
    final db = await appDatabase;
    return await db.getTaskDao.endCheck(userId , taskId ,t);
  }

  static Future updateCheckedDeviceCount(String userId ,String taskId, int checkedCount) async {
    final db = await appDatabase;
    return await db.getTaskDao.updateCheckedDeviceCount( userId , taskId,  checkedCount).notifyDatabase('IrTask');
  }

  static Future updateUploadStats(String userId ,String taskId , int hasUpload) async {
    final db = await appDatabase;
    return await db.getTaskDao.updateUploadStatus( userId , taskId,  hasUpload).notifyDatabase('IrTask');
  }

  // ****************************** 任务 ******************************


  /// 当前是否存在ir数据
  static Future<bool> existIrData(String userId) async {
      final db = await appDatabase;
      var list = await db.getChestStateDao.findCurrentState(userId);
      return list.isNotEmpty;
  }

  /// 当前ir 是否有未上传的数据
  static Future<bool> existIrUpload(String userId) async {
    final db = await appDatabase;
    var list = await db.getChestStateDao.findAllState(userId);
    return list.isNotEmpty;
  }

  // chest： chestList
  static Future<List<IrChest>> getIrChestList(String userId) async {
      final db = await appDatabase;
      return await db.getChestDao.findAllDevices(userId);
  }

  static Future<List<IrChest>> getIrChestListByTask(String userId , String taskId) async {
    final db = await appDatabase;
    return await db.getChestDao.findAllChestByTaskId(userId , taskId);
  }

  static Future updateChestCheckedDeviceCount(String userId ,String taskId,String chestId, int checkedCount) async {
    final db = await appDatabase;
    return await db.getChestDao.updateCheckedDeviceCount( userId , taskId, chestId, checkedCount).notifyDatabase('IrChest');
  }
  static Stream<List<IrChest>> listenChestList(String userId) async* {
    final db = await appDatabase;
    yield* db.getChestDao.listenChestList(userId).map((event) {
      return event;
    });
  }
  static Stream<List<IrChest>> listenChestListByTask(String userId,String taskId) async* {
    final db = await appDatabase;
    yield* db.getChestDao.listenChestListByTask(userId,taskId).map((event) {
      return event;
    });
  }

  /// 获取 所有设备列表
  static Future<List<IrChestDevice>> getIrDeviceList(String userId, String chestId) async {
      final db = await appDatabase;
      return await db.getChestDeviceDao.findAllDevicesByChest(userId, chestId);
  }

  static Future<List<IrChestDevice>> findAllDevicesByTask(String userId, String taskId) async {
      final db = await appDatabase;
      return await db.getChestDeviceDao.findAllDevicesByTask(userId, taskId);
  }
  static Future<List<IrChestDevice>> findAllDevicesByTaskAndChest(String userId, String taskId,String chestId) async {
    final db = await appDatabase;
    return await db.getChestDeviceDao.findAllDevicesByTaskAndChest(userId,taskId, chestId);
  }

  static Future<List<IrChestDevice>> getIrDeviceListByUser(String userId) async {
    final db = await appDatabase;
    return await db.getChestDeviceDao.findAllDevices(userId);
  }

  // 更新 chest list
  static Future<void> insertChestList(List<IrChest> list) async {
    final db = await appDatabase;
    return await db.getChestDao.insertList(list);
  }

  // 更新 chest list
  static Future<void> insertChestDevices(List<IrChestDevice> list) async {
    final db = await appDatabase;
    return await db.getChestDeviceDao.insertList(list);
  }

  // 更新 ir state
  static Future<void> insertIrState(String userId) async {
    final db = await appDatabase;
    var state = IrChestState(userId, 0 , 0);
    return await db.getChestStateDao.insertState(state);
  }

  // 删除 chest 信息
  static Future<void> clearChestList(String userId) async {
    final db = await appDatabase;
    return await db.getChestDao.clear(userId);
  }

  // 查询设备里 是否有没有巡检完的
  static Future<bool> findUnIrChestList(String userId) async {
    final db = await appDatabase;
    var list = await db.getChestDao.findAllDevices(userId);
    var has = list.where((e) => e.deviceCount > 0 && e.progress != '100').toList().length;
    return has > 0;
  }


  // 删除 chest devices 信息
  static Future<void> clearChestDeviceList(String userId) async {
    final db = await appDatabase;
    return await db.getChestDeviceDao.clear(userId);
  }

  static Future<void> clearIrState(String userId) async {
    final db = await appDatabase;
    return await db.getChestStateDao.clear(userId);
  }

  // chest 更新
  static Future updateIrChestProgress(String userId ,String chestId, String progress) async {
    final db = await appDatabase;
    return await db.getChestDao.updateProgress( userId , chestId,  progress).notifyDatabase('IrChest');
  }

  static Future<IrChestState?> currentIrState(String userId) async {
    final db = await appDatabase;
    var states = await db.getChestStateDao.findCurrentState(userId);
    if(states.isEmpty) return null;
    return states[0];
  }

  // 更新设备温度和备注 ，和图片
  static Future updateIrDevice(String userId , String deviceId ,String temperature, String comments , String path, int detectionTime) async {
    final db = await appDatabase;
    await db.getChestDeviceDao.updateProgress(userId, deviceId, temperature, path,  comments , detectionTime);
  }
  static Future updateDeviceByTaskAndChest(String userId , String deviceId , String taskId, String chestId ,String temperature, String comments , String path, int detectionTime) async {
    final db = await appDatabase;
    await db.getChestDeviceDao.updateProgressByTaskAndChest(userId, deviceId, taskId,chestId,temperature, path,  comments , detectionTime);
  }
  static Future updateIrDeviceComments(String userId , String deviceId ,String comments , int detectionTime) async {
    final db = await appDatabase;
    await db.getChestDeviceDao.updateDeviceComments(userId, deviceId, comments , detectionTime);
  }
  static Future updateDeviceCommentsByTaskAndChest(String userId , String deviceId, String taskId, String chestId ,String comments , int detectionTime) async {
    final db = await appDatabase;
    await db.getChestDeviceDao.updateDeviceCommentsByTaskAndChest(userId, deviceId, taskId, chestId, comments , detectionTime);
  }
  static Future updateTemperatureAndPath(String userId , String deviceId ,String temperature,String path, int detectionTime) async {
    final db = await appDatabase;
    await db.getChestDeviceDao.updateTemperatureAndPath(userId, deviceId,temperature ,path , detectionTime);
  }


  static Stream<List<IrChestDevice>> listenDeviceList(String userId) async* {
    final db = await appDatabase;
    yield* db.getChestDeviceDao.listenDeviceList(userId).map((event) {
      return event;
    });
  }


  // 开始ir 巡检了， 更新时间状态
  static Future updateIrStateStartTime(String userId , {int start = 0, int end = 0}) async {
    final db = await appDatabase;
    var state = await db.getChestStateDao.findCurrentState(userId);
    if(state.isEmpty){
         await insertIrState(userId);
         state = await db.getChestStateDao.findCurrentState(userId);
    }
    var currentState = state[0];
    if(start > 0){
      currentState.startTimeStamp = start;
    }
    if(end > 0){
      currentState.endTimeStamp = end;
    }
    return await db.getChestStateDao.updateProgress(userId, currentState.startTimeStamp, currentState.endTimeStamp);
  }



}