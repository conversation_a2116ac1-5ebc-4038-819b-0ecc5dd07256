

import 'package:floor/floor.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest.dart';

/// 红外巡检： 柜子dao
@dao
abstract class IrChestDao {

  @Query('SELECT * FROM IrChest WHERE userId = :userId')
  Future<List<IrChest>> findAllDevices(String userId);

  @Query('SELECT * FROM IrChest WHERE userId = :userId AND taskId = :taskId')
  Future<List<IrChest>> findAllChestByTaskId(String userId , String taskId);

  // 更新进度
  @Query('UPDATE IrChest SET progress = :progress WHERE userId = :userId AND chestId = :chestId')
  Future<void> updateProgress(String userId ,String chestId, String progress);

  @Query('UPDATE IrChest SET checkedDeviceCount = :checkedDeviceCount WHERE userId = :userId AND chestId = :chestId AND taskId = :taskId')
  Future<void> updateCheckedDeviceCount(String userId ,String taskId, String chestId, int checkedDeviceCount);

  // 批量更新
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<IrChest> irchestList);

  // 单独
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertDevice(IrChest device);

  @Query('delete FROM IrChest WHERE userId = :userId')
  Future<void> clear(String userId);

  @Query('SELECT * FROM IrChest WHERE userId = :userId')
  Stream<List<IrChest>> listenChestList(String userId);

  @Query('SELECT * FROM IrChest WHERE userId = :userId AND taskId = :taskId')
  Stream<List<IrChest>> listenChestListByTask(String userId,String taskId);


}