

import 'package:floor/floor.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest.dart';

import 'ir_chest_device.dart';


/// 红外巡检： 列头柜设备 dao
@dao
abstract class IrChestDeviceDao {

  @Query('SELECT * FROM IrChestDevice WHERE userId = :userId')
  Future<List<IrChestDevice>> findAllDevices(String userId);

  @Query('SELECT * FROM IrChestDevice WHERE userId = :userId AND infraredDeviceTypeId = :chestId')
  Future<List<IrChestDevice>> findAllDevicesByChest(String userId , String chestId);

  @Query('SELECT * FROM IrChestDevice WHERE userId = :userId AND taskId = :taskId')
  Future<List<IrChestDevice>> findAllDevicesByTask(String userId , String taskId);

  @Query('SELECT * FROM IrChestDevice WHERE userId = :userId AND taskId = :taskId AND infraredDeviceTypeId = :chestId')
  Future<List<IrChestDevice>> findAllDevicesByTaskAndChest(String userId , String taskId, String chestId);

  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertDevice(IrChestDevice device);

  // 批量更新
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<IrChestDevice> irchestDeviceList);

  // 更新 ir 设备温度和备注
  @Query('UPDATE IrChestDevice SET comments = :comments, checkTemperature = :temperature, path = :path, detectionTime = :detectionTime WHERE userId = :userId AND irDeviceId = :deviceId ' )
  Future<void> updateProgress(String userId ,String deviceId, String temperature , String path ,String comments , int detectionTime);
  @Query('UPDATE IrChestDevice SET comments = :comments, checkTemperature = :temperature, path = :path, detectionTime = :detectionTime WHERE userId = :userId AND irDeviceId = :deviceId AND taskId = :taskId AND infraredDeviceTypeId = :chestId' )
  Future<void> updateProgressByTaskAndChest(String userId ,String deviceId, String taskId, String chestId, String temperature , String path ,String comments  , int detectionTime);

  // 更新设备 温度，图片
  @Query('UPDATE IrChestDevice SET checkTemperature = :temperature, path = :path, detectionTime = :detectionTime WHERE userId = :userId AND irDeviceId = :deviceId ' )
  Future<void> updateTemperatureAndPath(String userId ,String deviceId, String temperature , String path , int detectionTime);

  // 更新设备 注释
  @Query('UPDATE IrChestDevice SET comments = :comments, detectionTime = :detectionTime WHERE userId = :userId AND irDeviceId = :deviceId ' )
  Future<void> updateDeviceComments(String userId ,String deviceId , String comments , int detectionTime);
  @Query('UPDATE IrChestDevice SET comments = :comments, detectionTime = :detectionTime WHERE userId = :userId AND irDeviceId = :deviceId AND taskId = :taskId AND infraredDeviceTypeId = :chestId' )
  Future<void> updateDeviceCommentsByTaskAndChest(String userId ,String deviceId , String taskId, String chestId , String comments, int detectionTime);

  @Query('delete FROM IrChestDevice WHERE userId = :userId')
  Future<void> clear(String userId);

  @Query('SELECT * FROM IrChestDevice WHERE userId = :userId AND irDeviceId = :irDeviceId')
  Future<List<IrChestDevice>> getDeviceById(String userId , String irDeviceId);

  @Query('SELECT * FROM IrChestDevice WHERE userId = :userId')
  Stream<List<IrChestDevice>> listenDeviceList(String userId);
}