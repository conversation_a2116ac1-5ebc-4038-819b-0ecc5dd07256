

import 'package:floor/floor.dart';

import 'ir_task.dart';

/// 红外巡检： 任务
@dao
abstract class IrTaskDao {

  @Query('SELECT * FROM IrTask WHERE userId = :userId')
  Future<List<IrTask>> findAllTasks(String userId);

  // 更新进度
  @Query('UPDATE IrTask SET checkedDeviceCount = :checkedDeviceCount WHERE userId = :userId AND taskId = :taskId')
  Future<void> updateCheckedDeviceCount(String userId ,String taskId, int checkedDeviceCount);

  // 开始检查
  @Query('UPDATE IrTask SET startTime = :startTime WHERE userId = :userId AND taskId = :taskId')
  Future<void> startCheck(String userId ,String taskId, int startTime);

  // 结束检查
  @Query('UPDATE IrTask SET endTime = :endTime WHERE userId = :userId AND taskId = :taskId')
  Future<void> endCheck(String userId ,String taskId, int endTime);

  // 更新检测数
  @Query('UPDATE IrTask SET checkedDeviceCount = :checkedDeviceCount WHERE userId = :userId AND taskId = :taskId')
  Future<void> updateCheckedCount(String userId ,String taskId, int checkedDeviceCount);

  @Query('UPDATE IrTask SET hasUpLoad = :hasUpLoad WHERE userId = :userId AND taskId = :taskId')
  Future<void> updateUploadStatus(String userId ,String taskId, int hasUpLoad);

  // 批量更新
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertList(List<IrTask> taskList);

  // 单独
  @Insert(onConflict: OnConflictStrategy.replace)
  Future<void> insertDevice(IrTask task);

  @Query('delete FROM IrTask WHERE userId = :userId')
  Future<void> clear(String userId);

  @Query('SELECT * FROM IrTask WHERE userId = :userId')
  Stream<List<IrTask>> listenChestList(String userId);




}