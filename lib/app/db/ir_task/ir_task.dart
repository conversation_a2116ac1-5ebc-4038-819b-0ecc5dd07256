import 'dart:ui';

import 'package:floor/floor.dart';

import '../../utils/time_helper.dart';


// 红外巡检任务
@entity
class IrTask {

  @PrimaryKey()
  final String taskId;

  final String? inspector;  // 巡检员

  final String? inspectorId;  // 巡检员id (即 userId )

  final String? departId;

  final String? departName;

  final int? type;

  final int? status;

  final String userId;

  final int startTime;

  final int endTime;

  final int deviceCount;

  final int checkedDeviceCount;

  final bool? hasUpLoad;

  IrTask(
      this.taskId,
      this.inspector,
      this.inspectorId,
      this.departId,
      this.departName,
      this.type,
      this.status,
      this.userId,
      this.startTime,
      this.endTime,
      this.deviceCount ,
      this.checkedDeviceCount,
      this.hasUpLoad,
      );

  Color taskStatusColor() {
    if(startTime == 0){
      return Color(0xffFF7B1D);
    }
    if(startTime > 0 && endTime > 0){
      if(hasUpLoad == true) {
        return Color(0xffDFDFDF);
      }else {
        return Color(0xff4ADC21);
      }
    }
    return Color(0xff4ADC21);
  }

  String taskStatus() {
    if(startTime == 0){
      return "待开始";
    }
    if(startTime > 0 && endTime > 0){
      if(hasUpLoad == true) {
        return "已完成";
      }else {
        return "进行中";
      }
    }
    return "进行中";
  }

  String taskType() {
    if(type == 1){
      return '计划性检查';
    }else if(type == 2){
      return '临时性检查';
    }
    return '未知类型检查';
  }

  String startTimeShow() {
    if(startTime == 0) return '-';
    return TimeHelper.timeStamp2FullFormat(startTime);
  }

  String endTimeShow() {
    if(endTime == 0) return '-';
    return TimeHelper.timeStamp2FullFormat(endTime);
  }

  String getProgress() {
    var d = checkedDeviceCount/deviceCount;
    var progress = _formatPercentage(d).toString();
    return progress;
  }


  int _formatPercentage(double number) {
    return (number * 100).round();
  }

  @override
  String toString() {
    return 'IrTask{taskId: $taskId, inspector: $inspector, inspectorId: $inspectorId, departId: $departId, departName: $departName, checkCount: $checkedDeviceCount, type: $type , userId: $userId, startTime: $startTime, endTime: $endTime, deviceCount: $deviceCount}';
  }
}