

class DataVersion{
  final int id;
  final String version;

  const DataVersion({
    required this.version,
    required this.id,
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "version" : version,
      "id" : id,
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  // @override
  Map<String, dynamic> to<PERSON>son() {
    return {
      "version" : version,
      "id" : id,
    };
  }
}