


import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';

class UserInfo {
  final String eid;
  final String avatar;
  final String name;
  final String phone;
  final String? passWorld;
  final String? deptName;
  final String? technicalPost;
  final String livingPlace;
  final int? gender;
  final String? occupation;
  final String? roomTypeName;
  final int? createTime;
  final String floor;
  final String shifts;
  final String roomType;
  final String? patrolStartTime;
  final String? patrolEndTime;
  final int isUpload;
  final int patrolStartTimeStamp;
  final int patrolEndTimeStamp;
  final String companyId;
  final String companyName;
  final int deviceMaintainPer;

  const UserInfo({
    required this.eid,
    required this.avatar,
    required this.name,
    required this.phone,
    required this.passWorld,
    required this.deptName,
    required this.technicalPost,
    required this.livingPlace,
    required this.gender,
    required this.occupation,
    required this.roomTypeName,
    required this.createTime,
    required this.floor,
    required this.shifts,
    required this.roomType,
    this.patrolStartTime, 
    this.patrolEndTime,
    required this.isUpload,
    required this.patrolStartTimeStamp,
    required this.patrolEndTimeStamp,
    required this.companyId,
    required this.companyName,
    required this.deviceMaintainPer
  });

  String getSpiltOccupation() {
    if(StringUtil.isEmpty(occupation)) return '';
    if((occupation?.length ?? 0) > 9) return '${(occupation ?? '').substring(0,8)}...';
    return occupation ?? '';
  }

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "eid":eid,
      "avatar":avatar,
      "name":name,
      "phone":phone,
      "passWorld":passWorld,
      "deptName":deptName,
      "technicalPost":technicalPost,
      "livingPlace":livingPlace,
      "gender":gender,
      "occupation":occupation,
      "roomTypeName":roomTypeName,
      "createTime":createTime,
      "floor":floor,
      "shifts":shifts,
      "roomType":roomType,
      "patrolStartTime" : patrolStartTime,
      "patrolEndTime" : patrolEndTime,
      "isUpload" : isUpload,
      "patrolStartTimeStamp" : patrolStartTimeStamp,
      "patrolEndTimeStamp" : patrolEndTimeStamp,
      "companyId" : companyId,
      "companyName" : companyName,
      "deviceMaintainPer" : deviceMaintainPer,
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  Map<String, dynamic> toJson() {
    return {
      "eid":eid,
      "avatar":avatar,
      "name":name,
      "phone":phone,
      "passWorld":passWorld,
      "deptName":deptName,
      "technicalPost":technicalPost,
      "livingPlace":livingPlace,
      "gender":gender,
      "occupation":occupation,
      "roomTypeName":roomTypeName,
      "createTime":createTime,
      "floor":floor,
      "shifts":shifts,
      "roomType":roomType,
      "patrolStartTime" : patrolStartTime,
      "patrolEndTime" : patrolEndTime,
      "isUpload" : isUpload,
      "patrolStartTimeStamp" : patrolStartTimeStamp,
      "patrolEndTimeStamp" : patrolEndTimeStamp,
      "companyId" : companyId,
      "companyName" : companyName,
      "deviceMaintainPer" :deviceMaintainPer,
    };
  }

}
