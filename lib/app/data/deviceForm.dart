

class DeviceForm {
  final int? id;
  final String name;
  final String formId;
  final String deviceTypeId;
  final String inputType;
  final String outputType;
  final String inspectionName;
  final String? inspectionRangeBegin;
  final String? inspectionRangeEnd;
  final String? inputValue;
  final int? inputActive;
  String? userId;

  DeviceForm({
    this.id,
    required this.name,
    required this.formId,
    required this.deviceTypeId,
    required this.inputType,
    required this.outputType,
    required this.inspectionName, 
    required this.inspectionRangeBegin, 
    required this.inspectionRangeEnd, 
    required this.inputValue, 
    required this.inputActive, 
    this.userId, 
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "id":id,
      "name" : name,
      "formId" : formId,
      "deviceTypeId" : deviceTypeId,
      "inputType" : inputType,
      "outputType" : outputType,
      "inspectionName" : inspectionName,
      "inspectionRangeBegin" : inspectionRangeBegin,
      "inspectionRangeEnd" : inspectionRangeEnd,
      "inputValue" : inputValue,
      "inputActive" : inputActive,
      "userId" : userId,
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  Map<String, dynamic> toJson() {
    return {
      "id":id,
      "name" : name,
      "formId" : formId,
      "deviceTypeId" : deviceTypeId,
      "inputType" : inputType,
      "outputType" : outputType,
      "inspectionName" : inspectionName,
      "inspectionRangeBegin" : inspectionRangeBegin,
      "inspectionRangeEnd" : inspectionRangeEnd,
      "inputValue" : inputValue,
      "inputActive" : inputActive,
      "userId" : userId,
    };
  }
}