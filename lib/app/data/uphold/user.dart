
class User{
  final String? woid;
  final String? uid;
  final String? name;
  final int? gender;
  final String? phone;
  final int status;
  final int role;


  const User({
    required this.woid,
    required this.uid,
    required this.name,
    required this.gender,
    required this.phone,
    required this.status,
    required this.role,
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "woid":woid,
      "uid":uid,
      "name":name,
      "gender":gender,
      "phone":phone,
      "status":status,
      "role":role
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  // @override
  Map<String, dynamic> toJson() {
    return {
      "woid":woid,
      "uid":uid,
      "name":name,
      "gender":gender,
      "phone":phone,
      "status":status,
      "role":role
    };
  }
}