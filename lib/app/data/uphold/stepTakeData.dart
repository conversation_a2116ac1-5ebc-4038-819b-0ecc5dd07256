
class StepTakeData{
  final String stepName;
  final String stepField;
  final String? woid;
  final String? id;
  final int? operateTime;
  final String? operateSignature;
  final int? supervisorTime;
  final String? supervisorSignature;


  const StepTakeData({
    required this.woid,
    required this.stepName,
    required this.stepField,
    required this.id,
    required this.operateTime,
    required this.operateSignature,
    required this.supervisorTime,
    required this.supervisorSignature,
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      "woid":woid,
      "stepName":stepName,
      "stepField":stepField,
      "id":id,
      "operateTime":operateTime,
      "operateSignature":operateSignature,
      "supervisorTime":supervisorTime,
      "supervisorSignature":supervisorSignature,
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  // @override
  Map<String, dynamic> toJson() {
    return {
      "woid":woid,
      "stepName":stepName,
      "stepField":stepField,
      "id":id,
      "operateTime":operateTime,
      "operateSignature":operateSignature,
      "supervisorTime":supervisorTime,
      "supervisorSignature":supervisorSignature,
    };
  }
}