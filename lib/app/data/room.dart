

class Room {
  final String roomId;
  final String name;
  final int storey;
  final int deviceCount;
  final String note;
  final String type;
  final int isFinish;
  final int? isTaste;
  final int? isSound;
  final int? goTime;
  final int? sortRank;
  String? userId;

  Room({
    required this.roomId,
    required this.name,
    required this.storey,
    required this.deviceCount,
    required this.note,
    required this.type, 
    required this.isFinish, 
    required this.isTaste, 
    required this.isSound,
    required this.goTime,
    this.sortRank,
    this.userId
  });

  // Convert a Dog into a Map. The keys must correspond to the names of the
  // columns in the database.
  Map<String, dynamic> toMap() {
    return {
      'roomId': roomId,
      'name': name,
      'storey': storey,
      'deviceCount': deviceCount,
      'note': note,
      'type': type,
      'isFinish': isFinish,
      'isTaste': isTaste,
      'isSound': isSound,
      'goTime': goTime,
      'sortRank': sortRank,
      'userId':userId
    };
  }

  // Implement toString to make it easier to see information about
  // each dog when using the print statement.
  Map<String, dynamic> toJson() {
    return {
      'roomId': roomId,
      'name': name,
      'storey': storey,
      'deviceCount': deviceCount,
      'note': note,
      'type': type,
      'isFinish': isFinish,
      'isTaste': isTaste,
      'isSound': isSound,
      'sortRank': sortRank,
      'goTime': goTime
    };
  }

}