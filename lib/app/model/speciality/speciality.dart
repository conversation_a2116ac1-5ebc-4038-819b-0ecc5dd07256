class SpecialityModule {
  SpecialityModule({
    required this.code,
    required this.msg,
    required this.data,
  });
  late final int code;
  late final String msg;
  late final List<Speciality> data;
  
  SpecialityModule.fromJson(Map<String, dynamic> json){
    code = json['code'];
    msg = json['msg'];
    data = List.from(json['data']).map((e)=>Speciality.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['code'] = code;
    _data['msg'] = msg;
    _data['data'] = data.map((e)=>e.toJson()).toList();
    return _data;
  }
}

class Speciality {
  Speciality({
    required this.roomType,
    required this.roomTypeName,
    required this.deviceCount,
  });
  late final String roomType;
  late final String roomTypeName;
  late final int deviceCount;
  
  Speciality.fromJson(Map<String, dynamic> json){
    roomType = json['roomType'];
    roomTypeName = json['roomTypeName'];
    deviceCount = json['deviceCount'];
  }
  Map<String, dynamic> toMap() {
    return {
      'roomType': roomType,
      'roomTypeName': roomTypeName,
      'deviceCount': deviceCount,
    };
  }
  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['roomType'] = roomType;
    _data['roomTypeName'] = roomTypeName;
    _data['deviceCount'] = deviceCount;
    return _data;
  }
}