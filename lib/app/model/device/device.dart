class DeviceModule {
  DeviceModule({
    required this.code,
    required this.msg,
    required this.data,
  });
  late final int code;
  late final String msg;
  late final List<Data> data;
  
  DeviceModule.fromJson(Map<String, dynamic> json){
    code = json['code'];
    msg = json['msg'];
    data = List.from(json['data']).map((e)=>Data.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['code'] = code;
    _data['msg'] = msg;
    _data['data'] = data.map((e)=>e.toJson()).toList();
    return _data;
  }
}

class Data {
  Data({
    required this.roomId,
    required this.devices,
  });
  late final String roomId;
  late final List<Devices> devices;
  
  Data.fromJson(Map<String, dynamic> json){
    roomId = json['roomId'];
    devices = List.from(json['devices']).map((e)=>Devices.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['roomId'] = roomId;
    _data['devices'] = devices.map((e)=>e.toJson()).toList();
    return _data;
  }
}

class Devices {
  Devices({
    required this.deviceId,
    required this.deviceTypeName,
    required this.deviceType,
    required this.deviceCode,
    required this.roomId,
    required this.roomType,
  });
  late final String deviceId;
  late final String deviceTypeName;
  late final String deviceType;
  late final String deviceCode;
  late final String roomId;
  late final String roomType;
  
  Devices.fromJson(Map<String, dynamic> json){
    deviceId = json['deviceId'];
    deviceTypeName = json['deviceTypeName'];
    deviceType = json['deviceType'];
    deviceCode = json['deviceCode'];
    roomId = json['roomId'];
    roomType = json['roomType'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['deviceId'] = deviceId;
    _data['deviceTypeName'] = deviceTypeName;
    _data['deviceType'] = deviceType;
    _data['deviceCode'] = deviceCode;
    _data['roomId'] = roomId;
    _data['roomType'] = roomType;
    return _data;
  }
}