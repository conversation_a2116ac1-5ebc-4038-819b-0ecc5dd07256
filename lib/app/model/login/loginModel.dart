class LoginModel {
  LoginModel({
    required this.code,
    required this.msg,
    required this.data,
  });
  late final int code;
  late final String msg;
  late final Data data;

  LoginModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    msg = json['msg'];
    data = Data.fromJson(json['data']);
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['code'] = code;
    _data['msg'] = msg;
    _data['data'] = data.toJson();
    return _data;
  }
}


class Data {
  Data({
    this.eid,
    required this.name,
    required this.phone,
    required this.passWorld,
    this.deptName,
    this.technicalPost,
    this.deptPost,
    required this.gender,
    this.occupation,
    this.roomTypeName,
    this.createTime,
    required this.floor,
    required this.shifts,
    required this.roomType,
    required this.roomTypeInfoList,
    required this.number,
    required this.versionName,
    required this.apkName,
    required this.versionCreateTime,
    required this.description,
    required this.url,
    this.livingPlace,
    required this.companyName,
    required this.companyId,
    required this.deviceMaintainPer,
    required this.token,
  });
  late final String? eid;
  late final String name;
  late final String phone;
  late final String passWorld;
  late final String? deptName;
  late final String? technicalPost;
  late final String? deptPost;
  late final int gender;
  late final String? occupation;
  late final String? roomTypeName;
  late final int? createTime;
  late final String? floor;
  late final String? shifts;
  late final String? roomType;
  late final String? livingPlace;
  late final String? number;
  late final String? versionName;
  late final String? apkName;
  late final int? versionCreateTime;
  late final String? description;
  late final String? url;
  late final List<RoomTypeInfoList>? roomTypeInfoList;
  late final String companyName;
  late final String companyId;
  late final int deviceMaintainPer;
  late final String? token;

  Data.fromJson(Map<String, dynamic> json) {
    print('*********+${json['deviceMaintainPer']}');
    eid = json['eid'];
    name = json['name'];
    phone = json['phone'];
    passWorld = json['passWorld'];
    deptName = json['deptName'];
    technicalPost = json['technicalPost'];
    deptPost = json['deptPost'];
    gender = json['gender'];
    occupation = json['occupation'];
    roomTypeName = json['roomTypeName']??'';
    createTime = json['createTime'];
    floor = json['floor'];
    shifts = json['shifts'];
    roomType = json['roomType'];
    livingPlace = json['livingPlace'];
    number = json['number'];
    versionName = json['versionName'];
    apkName = json['apkName'];
    versionCreateTime = json['versionCreateTime'];
    description = json['description'];
    token = json['token'];
    url = json['url'];
    companyName = json['companyName'];
    companyId = json['companyId'];
    if (json['deviceMaintainPer'] is String && json['deviceMaintainPer'] == '1') {
      deviceMaintainPer = 1;
    } else if (json['deviceMaintainPer'] is int && json['deviceMaintainPer'] == 1) {
      deviceMaintainPer = 1;
    } else {
      deviceMaintainPer = 0;
    }
    if (json['roomTypeInfoList'] == null) {
      roomTypeInfoList = [];
    } else {
      roomTypeInfoList = List.from(json['roomTypeInfoList'])
          .map((e) => RoomTypeInfoList.fromJson(e))
          .toList();
    }
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['eid'] = eid;
    _data['name'] = name;
    _data['phone'] = phone;
    _data['passWorld'] = passWorld;
    _data['deptName'] = deptName;
    _data['technicalPost'] = technicalPost;
    _data['deptPost'] = deptPost;
    _data['gender'] = gender;
    _data['occupation'] = occupation;
    _data['roomTypeName'] = roomTypeName;
    _data['createTime'] = createTime;
    _data['floor'] = floor;
    _data['shifts'] = shifts;
    _data['roomType'] = roomType;
    _data['livingPlace'] = livingPlace;
    _data['number'] = number;
    _data['versionName'] = versionName;
    _data['token'] = token;
    _data['apkName'] = apkName;
    _data['versionCreateTime'] = versionCreateTime;
    _data['description'] = description;
    _data['url'] = url;
    _data['roomTypeInfoList'] =
        roomTypeInfoList?.map((e) => e.toJson()).toList();
    _data['passWorld'] = passWorld;
    _data['companyName'] = companyName;
    _data['companyId'] = companyId;
    _data['deviceMaintainPer'] = deviceMaintainPer;
    return _data;
  }
}

class RoomTypeInfoList {
  RoomTypeInfoList({
    required this.roomType,
    required this.roomTypeName,
    required this.roomCount,
    required this.deviceCount,
  });
  late final String roomType;
  late final String roomTypeName;
  late final int roomCount;
  late final int deviceCount;

  RoomTypeInfoList.fromJson(Map<String, dynamic> json) {
    roomType = json['roomType'];
    roomTypeName = json['roomTypeName']??'';
    roomCount = json['roomCount'];
    deviceCount = json['deviceCount'];
  }

  Map<String, dynamic> toJson() {
    final _data = <String, dynamic>{};
    _data['roomType'] = roomType;
    _data['roomTypeName'] = roomTypeName;
    _data['roomCount'] = roomCount;
    _data['deviceCount'] = deviceCount;
    return _data;
  }
}
