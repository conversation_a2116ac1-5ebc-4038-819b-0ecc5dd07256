

// roomApi
///  static const String baseUrl = "http://*********:8884";// 开发
//   // static const String baseUrl = "http://test.inspection.ddbes.com:8080";// 开发
//   // static const String baseUrl = "http://*************:80/inspection"; // 内网


// useapi
/// // static const String baseUrl = 'http://*********:8593'; //开发
//   // static const String baseUrl = 'http://test.rangeidc.ddbes.com:8080'; //测试
//   static const String baseUrl = 'http://*************:80/center'; // 内网

class Host {
  static String roomApi = "http://*********:8884";

  static String userApi = "http://*********:8593";

  // static String uploadApi = "http://*********:8593";


}


enum Env { Product, Dev , Test ,Intranet}

class EnvConfig {
  static Env mEnv = Env.Product;

  static isProduct() {
    return mEnv == Env.Product;
  }

  static switchEnv(Env env) {
     mEnv = env;
     switch(env) {
       case Env.Product:
         Host.roomApi = "http://*************:80/inspection";
         Host.userApi = "http://*************:80/center";

         break;

       case Env.Dev:
         Host.roomApi = "http://*********:8884";
         Host.userApi = "http://*********:8593";
         // Host.roomApi = "http://*************:80/inspection";
         // Host.userApi = "http://*************:80/center";

         break;

       case Env.Test:
         Host.roomApi = "http://test.inspection.ddbes.com:8088";
         Host.userApi = "http://test.rangeidc.ddbes.com:8088";

         break;

       case Env.Intranet:
         Host.roomApi = "http://*************:80/inspection";
         Host.userApi = "http://*************:80/center";
         break;

       default:

         break;
     }
  }

}

enum DEVICE { phone , pad}

class DeviceConfig {

  static DEVICE mDevice = DEVICE.pad;

  static Future<bool> isPad() async {
    return mDevice == DEVICE.pad;
  }

  static switchDevice(DEVICE device) {
    mDevice = device;
  }
}