import 'package:sjzx_patrol_system_mobile/app/api/env_config.dart';

import '../utils/HttpsClient.dart';

class UserApi {
  // static const String baseUrl = 'http://*********:8593'; //开发
  // static const String baseUrl = 'http://test.rangeidc.ddbes.com:8080'; //测试
  // static const String baseUrl = 'http://*************:80/center'; // 内网
  late HttpsClient httpsClient;
  UserApi(){
    httpsClient = HttpsClient();
  }

  // 根据工号获取员工信息
  login(data) async {
    var response = await httpsClient.post("${Host.userApi}/user/pda/login/v1",data);
    return response;
  }

  // login([queryParameters]) async {
  //   var response = await httpsClient.get("api/httpGet");dd
  //   return response;
  // }
  


}