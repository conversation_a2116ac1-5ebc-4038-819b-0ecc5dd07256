import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';

extension NumberGapEx on num {
  Gap get gap => Gap(this.toDouble(),);
  SliverGap get sliverGap => SliverGap(this.toDouble(),);
  //按照系统字体放大比例缩放
  double get fitScaleSize => MediaQueryData.fromWindow(window).textScaleFactor*this.toDouble();
  //设置最大放大比例
  double get maxScale => _getMaxScale();
  _getMaxScale() {
    double currentScaleFactor = MediaQueryData.fromWindow(window).textScaleFactor;
    if (currentScaleFactor > this.toDouble()) {
      return this.toDouble();
    }
    return currentScaleFactor;
  }
}