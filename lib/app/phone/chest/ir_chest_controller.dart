import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sjzx_patrol_system_mobile/app/base/base_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/string_extension.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/ir_detection/widget/ir_dialog.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/time_helper.dart';

import '../../controllers/sqflite_controller.dart';
import '../../utils/screenutil.dart';
import '../../utils/string_util.dart';


class IrChestController extends BaseGetController {
  SQfliteController sqfliteController = Get.find();
  StreamSubscription? chestStream;
  /**扫一扫相关配置 */
  // 相机和外部存储权限(获取安卓权限)
  final permissions = const [
    Permission.storage,
    Permission.camera
    // Permission.READ_EXTERNAL_STORAGE,
    // Permissions.CAMERA
  ];
  // 相机和外部存储权限(获取iod权限)
  final permissionGroup = const [
    Permission.storage,
    Permission.photos
    // PermissionGroup.Photos
  ];

  // 房间类型列表
  List roomTypeInfoList = [];

  final count = 0.obs;

  String taskId = '';

  @override
  void onInit() async{
    super.onInit();
    taskId = Get.arguments['taskId'];
    if(userId.isEmptyOrNull() || taskId.isEmptyOrNull()){
      toast('当前未登录');
      Get.back();
    }
  }

  /// db 查询 （列头柜）
  void queryIrChestList(List<IrChest?>? event) async {
    // print("=====chest change====");
    var chestList = await DBHelper.getIrChestListByTask(userId ,taskId);
    roomTypeInfoList = chestList;
    update();
  }

  @override
  void onReady() async {
    super.onReady();
    chestStream = DBHelper.listenChestListByTask(userId,taskId).listen(queryIrChestList);

    queryIrChestList([]);
    _checkIrIsOver();
  }
  @override
  void onClose(){
    super.onClose();
    chestStream?.cancel();
  }

  _checkIrIsOver() async {
    var state = await DBHelper.currentIrState(userId);
    if(state == null) return;
    var hasData = await _hasUnIrData();
    if(state.endTimeStamp > 0 && hasData){
      ifFinish();
    }
  }

  // 判断是否已经巡检结束
  ifFinish() async {
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: const [Text("提示")],
          ),
          content: Row(
            children: const [Text("请将此次巡检数据上传后在重新巡检")],
          ),
          actions: [
            ElevatedButton(
                onPressed: () {
                  Get.back();
                  Get.back();
                },
                child: const Text("返回首页"),
                style: ButtonStyle(backgroundColor:
                    MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                }))),
          ],
        ));
  }

  // void endIrPatrol () async {
  //     var state = await DBHelper.currentIrState(userId);
  //     if(state == null) return;
  //
  //     var hasData = await _hasUnIrData();
  //     if(!hasData) {
  //       toast('您还未开始巡检设备，不能结束巡检');
  //       return;
  //     }
  //
  //
  //     // 查询所有设备进度是否 都是100%
  //     var hasUnIr = await DBHelper.findUnIrChestList(userId);
  //
  //     if(state.endTimeStamp > 0) {
  //       toast('巡检已结束，请上传数据');
  //       return;
  //     }
  //
  //     var message = "此次巡检未完成,是否结束巡检";
  //
  //     if(!hasUnIr) {
  //       message = "是否结束此次巡检";
  //     }
  //
  //     IrDialog.showDialog('提示', '$message', '确定', '我再想想' , confirmCall: (){
  //       var date = DateTime.now();
  //       DBHelper.updateIrStateStartTime(userId , start:state.startTimeStamp , end: date.millisecondsSinceEpoch);
  //       Get.back();
  //     });
  // }

  Future<bool> _hasUnIrData() async {
    var todoList = await DBHelper.findAllDevicesByTask(userId,taskId);
    var datas = todoList.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList();
    return datas.isNotEmpty;
  }

}
