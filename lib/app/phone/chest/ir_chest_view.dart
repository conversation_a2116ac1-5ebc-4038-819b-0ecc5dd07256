import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/chest/ir_chest_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/app_pages.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/route_helper.dart';
import '../../db/ir/ir_chest.dart';
import '../../utils/permission/permission_util.dart';
import '../common/phone_tool_bar.dart';
import 'chest_card.dart';


/// phone
class IrChestView extends StatelessWidget {

  const IrChestView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<IrChestController>(builder: (_){
      return PhoneToolBar(
        title: '设备类型',
        body: buildChestList(context , _),
      );
    });
  }

  // 设备
  Widget buildChestList(BuildContext context , IrChestController controller) {
    return Container(
      margin: const EdgeInsets.all(16),
      child: GridView.builder(gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.4,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
          itemCount: controller.roomTypeInfoList.length,
          itemBuilder: (ctx ,index){
            IrChest irChest = controller.roomTypeInfoList[index];
            return ChestCard(irChest: irChest , click: () async {
              var hasPermission = await PermissionUtil.checkCameraPermission(context , tip: scanPermissionTip );
              if (!hasPermission) return;
              var argument = {
                "chestId" : irChest.chestId,
                "taskId" : irChest.taskId
              };
              route(Routes.PHONE_DEVICES , params: argument);
            });
          })
    );
  }
}
