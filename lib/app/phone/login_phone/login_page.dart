import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/screenutil.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

import '../widget/theme.dart';
import 'login_phone_controller.dart';

/// 登录页
class LoginPhonePage extends StatelessWidget {

  const LoginPhonePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PhoneLoginController>(builder: (_){
      return Scaffold(
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: true,
        body: Container(
          child: Stack(
            children: [
              Image.asset(AssetsRes.PHONE_LOGIN_BG,
                width: double.infinity,
                fit: BoxFit.fitWidth,),
              Column(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                      flex: 106,
                      child: Container()),

                  Expanded(
                      flex: 301,
                      child: Container(
                        margin: EdgeInsets.only(left: 40
                        ),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(topLeft: Radius.circular(60),topRight: Radius.circular(30)
                              ,bottomLeft: Radius.circular(50),bottomRight: Radius.circular(0)),
                          // border: Border(bottom: )
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            42.gap,
                            Container(
                              alignment: Alignment.centerLeft,
                              margin: EdgeInsets.only(left: 32),
                              child: const Text('欢迎登录',style: TextStyle(fontSize: 22 , color: Colors.black , fontWeight: FontWeight.w500)),
                            ),
                            Container(
                              margin: const EdgeInsets.only(left: 32, top: 8),
                              child: const Text('数据中心综合管理平台',style: TextStyle(fontSize: 16 , color: Colors.black)),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 32,top: 34, right: 32),
                              child: Row(
                                children: [
                                  Image.asset(AssetsRes.PHONE_MOBILE_ICON ,width: 15,),
                                  5.gap,
                                  Expanded(child: Container(
                                    child: TextField(
                                        inputFormatters: <TextInputFormatter>[
                                          LengthLimitingTextInputFormatter(11) //限制长度
                                        ],
                                        style: const TextStyle(
                                          fontSize: 15,
                                          color: Colors.black,
                                        ),
                                        controller: _.textEditingController,
                                        decoration: InputDecoration(
                                            border:InputBorder.none,
                                            hintText: "请输入手机号",
                                            contentPadding: EdgeInsets.only(
                                              top: MyScreenUtil.height(10),
                                              bottom:MyScreenUtil.height(10),
                                              left:MyScreenUtil.height(10),
                                              right: MyScreenUtil.height(10),
                                            )
                                        ),
                                        onChanged: (value) {
                                          _.setUserCode(value);
                                        }
                                    ),
                                  )),
                                ],

                              ),
                            ),

                            Container(
                              height: 1,
                              margin: const EdgeInsets.symmetric(horizontal: 32),
                              color: const Color(0xFFe0e0e0),
                            ),

                            Container(
                                margin: const EdgeInsets.only(left: 32 ,right: 32,top: 24),
                                child: Row(
                                  children: [
                                    Image.asset(AssetsRes.PHONE_PWD_ICON ,width: 15,),
                                    5.gap,
                                    Expanded(child: TextField(
                                        obscureText:true,
                                        controller: _.textPasswordController,
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 15,
                                        ),
                                        decoration: InputDecoration(
                                            border:InputBorder.none,
                                            hintText: "请输入密码",
                                            contentPadding: EdgeInsets.only(
                                              top: MyScreenUtil.height(10),
                                              bottom:MyScreenUtil.height(10),
                                              left:MyScreenUtil.height(10),
                                              right: MyScreenUtil.height(10),
                                            )
                                        ),
                                        onChanged: (value) {
                                          _.setUserPassword(value);
                                        }
                                    ))
                                  ],
                                )
                            ),

                            Container(
                              height: 1,
                              margin: const EdgeInsets.symmetric(horizontal: 32),
                              color: const Color(0xFFe0e0e0),
                            ),

                            Row(
                              children: [
                                Expanded(child: InkWell(
                                  onTap: (){
                                    FocusScopeNode currentFocus = FocusScope.of(context);
                                    if (!currentFocus.hasPrimaryFocus) {
                                      currentFocus.unfocus();
                                    }
                                    _.login();
                                  },
                                  child: Container(
                                    margin: const EdgeInsets.only(left: 16,top: 42,right: 16),
                                    decoration:  BoxDecoration(
                                      color: _.hasFiledMobileAndPwd() ? const Color(0xFF5777FF) : const Color(0xff8AADFC),
                                      borderRadius: BorderRadius.circular(80.0),
                                    ),
                                    padding: const EdgeInsets.symmetric(vertical: 7),
                                    alignment: Alignment.center,
                                    child: const Text("登录", style: TextStyle(fontSize: 17,color: Colors.white),),
                                  ),
                                ))
                              ],
                            ),


                          ],
                        ),
                      ))


                ],
              )
            ],
          ),
        ),
      );
    });
  }


}