

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

class PhoneToolBar extends StatelessWidget {

  final String? title;
  final Widget? body;

  const PhoneToolBar({super.key,this.title,this.body});

  @override
  Widget build(BuildContext context) {
    SystemUiOverlayStyle overlayStyle =  const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    );

    return WillPopScope(
        onWillPop: () async {
          return true;
        },
        child: Scaffold(
            appBar: AppBar(
              leadingWidth: 40,
              leading: InkWell(
                onTap: (){
                  Get.back();
                },
                child: Container(
                  color: Colors.transparent,
                  padding: const EdgeInsets.only(left: 20 ,right: 10),
                  child: Image.asset(AssetsRes.PHONE_TOOL_BACK),
                ),
              ),
              centerTitle: true,
              title: Text('$title' , style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16, color: Color(0xff1D2129)),),
              elevation: 0,
              backgroundColor: Colors.white,
              systemOverlayStyle: overlayStyle,
            ), body: Container(
                    color: Color(0xfff2f3f5),
                    child: body ?? Container(),
        )));
  }

}