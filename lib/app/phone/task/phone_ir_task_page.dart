import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir_task/ir_task.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/phone_tool_bar.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/task/phone_ir_task_controller.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';
import '../../routes/app_pages.dart';
import '../../routes/route_helper.dart';

/// 巡检任务
class PhoneIrTaskPage extends StatelessWidget {

  const PhoneIrTaskPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PhoneIrTaskController>(builder: (_) {
      return PhoneToolBar(
        title: _.getTitle(),
        body: _.uiTaskList.isEmpty ? Container(
          color: Colors.white,
          alignment: Alignment.center,
          child: Text(_.getEmptyTip()),
        ) : Container(
          padding: EdgeInsets.only(top: 8),
          child: ListView.builder(
              itemCount: _.uiTaskList.length,
              itemBuilder: (ctx , index){
                var body = _.uiTaskList[index];
                return taskItem(context, body , _);
              }),
        ),
      );
    });
  }

  taskItemRow(String label,String? value, PhoneIrTaskController controller) {
    return Container(
      child: Row(
        children: [
          Text('$label' ,style: TextStyle(fontSize: 14 ,color: Colors.black),),
          3.gap,
          const Text(':' ,style: TextStyle(fontSize: 14 ,color: Colors.black),),
          3.gap,
          Text('$value' ,style: TextStyle(fontSize: 14 ,color: Colors.black),)
        ],
      ),
    );
  }

  // 巡检任务 item
  Widget taskItem(BuildContext context ,IrTask body, PhoneIrTaskController controller) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16 ,vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Colors.white,
      ),
      child: Container(
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 65,
                  height: 65,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(100),
                      image: const DecorationImage(
                          image: AssetImage(AssetsRes.USERLOGO),
                          fit: BoxFit.fill)),
                ),

                Expanded(child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                    children: [
                      Text(body.inspector ?? '',
                          style: const TextStyle(
                              color: Colors.black,
                              fontSize: 14,
                              fontWeight: FontWeight.w600)),

                      9.gap,
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: const Color(0xffE8EDFF),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Text(body.taskType() ?? '暂无'
                          , style: const TextStyle(fontSize: 12,color: Color(0xff4F70FD)),),
                      ),
                      Expanded(child: Container()),

                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                            color: body.taskStatusColor(),
                            borderRadius: BorderRadius.circular(100),
                           ),
                      ),
                      3.gap,
                      Text(body.taskStatus()
                        , style: const TextStyle(fontSize: 12,color: Colors.black),),
                    ],
                  ),
                    5.gap,
                    Text(body.departName ?? '',
                        style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                        )),


                ],))

              ],
            ),

            10.gap,

            Row(
              children: [
                Expanded(child: taskItemRow('检查设备数量', '${body.deviceCount ?? 0}', controller),),
                Expanded(child: taskItemRow('检查进度', "${body.getProgress()}%" , controller),),
              ],
            ),
            8.gap,
            taskItemRow('检查开始时间', body.startTimeShow() , controller),

            8.gap,
            taskItemRow('检查结束时间', body.endTimeShow() , controller),

            10.gap,

            Container(
              height: 1,
              color: Color(0xffF3F3F3),
            ),

            10.gap,

            controller.isRecord ? Container():
            Row(
              children: [
              _buildStartCheckButton(body, () {
                controller.startCheck(body , () => route(Routes.PHONE_CHEST , params: {
                  'taskId': body.taskId})
                );
              }),
              _buildEndCheckButton(body, () => controller.endCheck(body)),
              _buildUploadButton(body, () => controller.uploadTask(body)),
            ],),

          ],
        ),
      ),
    );
  }

  _buildStartCheckButton(IrTask task , VoidCallback startAction) {
    if(task.startTime > 0 && task.endTime > 0){
      return Container();
    }
    var btnLabel = '开始检查';
    if(task.startTime == 0 ){
      btnLabel = '开始检查';
    }else if(task.startTime >0 && task.endTime == 0){
      btnLabel = '继续检查';
    }

    return Expanded(
      child: GestureDetector(
        onTap: () => startAction.call(),
        child: Container(
          color: Colors.transparent,
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(vertical: 6),
          child: Text(btnLabel , style: TextStyle(fontSize: 15 ,color: Color(0xff5777FF)),),
        ),
      )

    );
  }

  _buildEndCheckButton(IrTask task , VoidCallback endAction) {
    if(task.startTime == 0) return Container();
    if(task.startTime > 0 && task.endTime == 0) {
      return Expanded(
        child: GestureDetector(
          onTap: () => endAction.call(),
          child: Container(
            color: Colors.transparent,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 6),
            child: const Text('结束检查' , style: TextStyle(fontSize: 15 ,color: Color(0xffFF5757)),),
          ),
        ),
      );
    }
    return Container();
  }

  _buildUploadButton(IrTask task , VoidCallback uploadAction) {
    if(task.startTime > 0 && task.endTime > 0) {
      return Expanded(
        child: GestureDetector(
          onTap: () => uploadAction.call(),
          child: Container(
            color: Colors.transparent,
            alignment: Alignment.center,
            padding: const EdgeInsets.symmetric(vertical: 6),
            child: const Text('上传数据' , style: TextStyle(fontSize: 15 ,color: Color(0xff5777FF)),),
          ),
        ),
      );
    }
    return Container();
  }

}