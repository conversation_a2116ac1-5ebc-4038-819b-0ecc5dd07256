

import 'dart:async';
import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/db/ir/ir_chest_device.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/entity/upload_img_req.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/loading_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';

import '../../../main.dart';
import '../../api/env_config.dart';
import '../../base/base_controller.dart';
import '../../controllers/sqflite_controller.dart';
import '../../db/ir_task/ir_task.dart';
import '../../modules/ir_detection/model/ir_convert.dart';
import '../../retrofit/entity/ir_upload_req.dart';
import '../../retrofit/ir_datasource.dart';
import '../../utils/file_util.dart';
import '../../utils/string_util.dart';

class PhoneIrTaskController extends BaseGetController {

  SQfliteController sqfliteController = Get.find();
  StreamSubscription? taskStream;
  // 房间类型列表
  RxList roomTypeInfoList = [].obs;

  var isStartModel = true;

  List<IrTask> uiTaskList = [];

  Map userInfo = {};

  bool isRecord = false;

  RxInt uploadProgress = 0.obs;

  @override
  void onInit() async {
    super.onInit();
    isRecord = Get.arguments['isRecord'] ?? false;
    _fetchTaskList();
  }

  @override
  void onReady() {
    super.onReady();
    DBHelper.listenTaskList(userId).listen((event) =>  _fetchTaskList());
  }
  @override
  void onClose(){
    super.onClose();
    taskStream?.cancel();
  }

  String getTitle() {
    return isRecord == true ? '巡检记录' : '巡检任务';
  }

  String getEmptyTip() {
    return isRecord == true ? '没有巡检记录' : '没有巡检任务';
  }

  _fetchTaskList() async {
    var list = await DBHelper.getAllTasks(userId);
    uiTaskList.clear();
    uiTaskList.addAll(list.where((e) => e.hasUpLoad == isRecord));
    update();
  }
  startCheck(IrTask task , VoidCallback end) async {
    if(task.startTime == 0){
      var startTime = DateTime.now().millisecondsSinceEpoch;
      await DBHelper.startCheck(userId, task.taskId, startTime);
      _fetchTaskList();
    }
    end.call();
  }

  endCheck(IrTask task) async {
    if(task.checkedDeviceCount <= 0) {
      toast('您还没进设备检查');
      return;
    }

    var endTime = DateTime.now().millisecondsSinceEpoch;
    await DBHelper.endCheck(userId, task.taskId, endTime);
    _fetchTaskList();
  }

  Future _findUserInfo()async{
    var userInfoData = await sqfliteController.findUsers("eid='$userId'");
    if(userInfoData.isEmpty) return;
    var userItem = userInfoData[0];
    userInfo["eid"] = userItem.eid;
    userInfo["name"] = userItem.name;
    userInfo["avatar"] = userItem.avatar;
    userInfo["dept"] = userItem.deptName;
    userInfo["patrolStartTime"] = userItem.patrolStartTime;
    userInfo["patrolEndTime"] = userItem.patrolEndTime;
    userInfo['occupation'] = userItem.occupation;
    userInfo['technicalPost'] = userItem.technicalPost;
    update();
  }

  // 上传
  uploadTask(IrTask task) async {
    uploadProgress.value = 0;
    List<IrChestDevice> taskList = await DBHelper.findAllDevicesByTask(userId, task.taskId);
    var todoUploadList = taskList.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList();
    if(todoUploadList.isEmpty){
      toast('没有需要上传的数据');
      return;
    }

    // LoadingManager.showLoading();

    var userDataSource = IrDataSource(retrofitDio , baseUrl: Host.userApi);

    // var state = await DBHelper.currentIrState(userId);
    // if(state == null) return false;

    var todoList = todoUploadList.map((e) => convertIrRequestUploadBody(e)).toList();

    showProgressDialog(todoList.length);

    await Future.forEach(todoList, (e) async{
      // e.picture = await FileUtil.createBase64FromUrl("/storage/emulated/0/DCIM/Camera/IMG_20211106_204638697.jpg");
      var base64 = await FileUtil.createBase64FromUrl(e.picture ?? '');
      // UploadImgReq req = UploadImgReq(file: base64);
      var url = await userDataSource.uploadImg(base64);
      logger("===上传图片完成===${e.picture}====${url}=======");
      e.picture = url.data;
      uploadProgress++;
      if(uploadProgress.value == todoList.length){
        uploadProgress.value = 0;
        Get.back();
      }
    });

    _findUserInfo();

    var reqBody = IrUploadReq()
      ..userId = userId
      ..devicesCount = todoUploadList.length.toString()
      ..endTime = task.endTime
      ..startTime = task.startTime
      ..occupation = userInfo['occupation'] ?? ''
      ..infraredInspectionTaskId = task.taskId ?? ''
      ..deviceList = todoList;

    logger('上传ir： 请求参数====> ${reqBody.toString()}');

    var resp = await userDataSource.commitIrDevices(reqBody);

    // LoadingManager.disMiss();

    if(resp.code == 0){
      toast('${resp.msg}');
    }else {
      // 上传成功
      DBHelper.updateUploadStats(userId, task.taskId, 1);
    }
  }

  void showProgressDialog(int maxValue) {
    Get.dialog(
      barrierDismissible: false,
      AlertDialog(
        title:  Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: const [Text("上传图片",style: TextStyle(fontSize: 20),)],
        ),
        content: Obx(
          () => Container(
            height: 100,
            child: Container(
              margin: EdgeInsets.only(top: 20,bottom: 10),
              height: 50,
              child:Column(
                children: [
                  LinearProgressIndicator(
                    minHeight: 8,
                    backgroundColor: Colors.grey[200],
                    valueColor: const AlwaysStoppedAnimation(Colors.blue),
                    value: uploadProgress.value/maxValue,
                  ) ,
                  SizedBox(height: 5,),
                  Text("上传进度：${uploadProgress.value}/${maxValue}"
                  ,style: TextStyle(fontSize: 15),)
                ],
              )
            ),
          )
        ),
      )
    );
  }

}