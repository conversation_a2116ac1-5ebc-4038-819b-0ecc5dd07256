import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_ir_plugin/ir/surface_platform_plugin.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/base/base_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/db/db_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';

import '../../db/ir/ir_chest_device.dart';
import '../../utils/file_util.dart';
import '../../utils/logger.dart';
import '../../utils/screenutil.dart';
import '../../utils/string_util.dart';


class CameraController extends BaseGetController {

  TextEditingController imageNameTextEditingController = TextEditingController();

  String? temperature;

  var oldImagePath = '';

  IrChestDevice? currentDevice;

  String chestId = '';
  String taskId = '';
  var isSetUser = false;

  @override
  void onInit() {
    super.onInit();
    currentDevice = Get.arguments['device'];
    chestId = currentDevice?.infraredDeviceTypeId ?? '';
    taskId = currentDevice?.taskId ?? '';
    logger('当前设备的chestId: $chestId');

    try{
      SurfacePlatformPlugin.init((t) {
        temperature = t.toString();
        if(!isSetUser){
          isSetUser = true;
          SurfacePlatformPlugin.setCurrentUser(globalController.userInfo.value!.data.name);
        }
      }, (path) {
        oldImagePath = path;
        imageNameTextEditingController.text = realFileName(oldImagePath);
        _showModifyPictureNameDialog(Get.context!);
      });

    }catch(e){
      print('这是什么错误： $e');
    }
  }

  String getTitle() {
    return '${currentDevice?.installationSite ?? ''} ${currentDevice?.getFullName()}';
  }

  String realFileName(String path) {
    if(StringUtil.isEmpty(path)) return '';
    var picName = path.split('/').lastOrNull ?? '';
    return picName;
  }

  // 确认拍照图片名称
  _showModifyPictureNameDialog(BuildContext context) async {
    Get.dialog(
        barrierDismissible: false,
        AlertDialog(
          title: const Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [Text("提示")],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text("确认图片名称，可编辑修改"),
              TextField(
                controller: imageNameTextEditingController,
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 14,
                ),
              )
            ],

          ),
          actions: [
            ElevatedButton(
                onPressed: () {
                  Get.back();
                  reNameFile(imageNameTextEditingController.text);
                  Get.back();
                },
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return MyScreenUtil.ThemColor();
                })),
                child: const Text("确认")),

            ElevatedButton(
                onPressed: () async {
                  // 删除图片
                  await _deleteCurrentPicFile(oldImagePath);
                  Get.back();
                },
                style: ButtonStyle(backgroundColor:
                MaterialStateProperty.resolveWith((states) {
                  return Colors.white;
                })),
                child: const Text("取消", style: TextStyle(color: Colors.black),)),
          ],
        ));
  }

  reNameFile(String newFileName) async {
    logger('old:$oldImagePath ,new: $newFileName');
    if(newFileName == realFileName(oldImagePath)) {
      updateCurrentShot(oldImagePath);
      return;
    };
    try{
      var newPath = joinPath(oldImagePath , newFileName);
      await File(oldImagePath).copy(newPath);
      File(oldImagePath).delete();
      updateCurrentShot(newPath);
    }catch(e){
      logger(e);
    }
  }

  String joinPath(String oldPath ,String fileName) {
    int lastSlashIndex = oldPath.lastIndexOf('/');
    String directoryPath = oldPath.substring(0, lastSlashIndex);
    String newPath = directoryPath + fileName;
    logger('新路径$newPath');
    return newPath;
  }

  /// 有温度和备注， 1 更新当前数据库， 2 更新进度
  Future _updateIrDevice2Db() async {
    if(currentDevice == null) return false;
    currentDevice?.checkTemperature = temperature ?? '';
    if(StringUtil.isEmpty(currentDevice?.checkTemperature)){
      toast('没有检测到温度');
      return false;
    }
    if(StringUtil.isEmpty(currentDevice?.path)) {
      toast('未检测到图片路径');
      return false;
    };
    var detectionTime = DateTime.now().millisecondsSinceEpoch;
    await DBHelper.updateDeviceByTaskAndChest(userId ,currentDevice!.irDeviceId ,taskId,chestId, currentDevice!.checkTemperature , currentDevice!.comments ?? '',
        currentDevice!.path,
        detectionTime);
    var chestDeviceList = await DBHelper.findAllDevicesByTaskAndChest(userId, taskId,chestId);
    if(chestDeviceList.isNotEmpty){
      var finishCount = chestDeviceList.where((e) => !StringUtil.isEmpty(e.checkTemperature)).toList().length;
      if(finishCount == 0) return;

      //更新 chest 进度
      DBHelper.updateChestCheckedDeviceCount(userId, taskId, chestId, finishCount);

      //更新task进度
      var taskdevices = await DBHelper.findAllDevicesByTask(userId, currentDevice?.taskId ??'');
      var taskFinishCount = taskdevices.where((e) => !StringUtil.isEmpty(e.path)).toList().length;
      DBHelper.updateCheckedDeviceCount(userId, currentDevice?.taskId ??'', taskFinishCount);
    }
    return true;
  }

  void updateCurrentShot(String imagePath) async {
    if(StringUtil.isEmpty(imagePath)){
      toast('图片地址不存在');
      return;
    }

    currentDevice?.path = imagePath ?? '';
    update();
    var result = await _updateIrDevice2Db();
    if(result){
      toast('保存成功');
    }
  }

  Future _deleteCurrentPicFile(String filePath) async {
    try{
      logger('删除文件 $filePath');
      await FileUtil.deleteFileByPath(filePath);
    }catch(e){}
  }

  void takePhoto() async {
    try{
      // todo
      // currentDevice?.path = 'dd';
      // currentDevice?.checkTemperature = '38';
      // _updateIrDevice2Db();
      // return;


      var temperature = await SurfacePlatformPlugin.takePhoto();
    }catch(e){
      logger('takePhoto...$e');
    }
  }

}
