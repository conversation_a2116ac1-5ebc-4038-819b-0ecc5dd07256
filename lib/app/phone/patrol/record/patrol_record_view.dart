import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/phone_tool_bar.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/patrol/record/patrol_record_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/time_helper.dart';

class PhonePatrolRecordView extends StatelessWidget {
  const PhonePatrolRecordView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PhonePatrolRecordController>(
        builder: (controller) {
          return Scaffold(
            backgroundColor: const Color(0xfff2f3f5),
            body: Column(
              children: [
                const PhoneToolBar(title: '巡检记录'),
                20.gap,
                _buildContent(controller),
              ],
            ),
          );
        }
    );
  }

  Widget _buildContent(PhonePatrolRecordController controller) {
    return Expanded(
      child: controller.historyList.isEmpty
          ? _buildEmptyView()
          : _buildRecordList(controller),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.history,
            size: 60,
            color: Colors.grey,
          ),
          10.gap,
          const Text(
            '暂无巡检记录',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordList(PhonePatrolRecordController controller) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: controller.historyList.length,
      itemBuilder: (context, index) {
        var item = controller.historyList[index];
        return _buildRecordItem(item, controller);
      },
    );
  }

  Widget _buildRecordItem(dynamic item, PhonePatrolRecordController controller) {
    String startTime = item.patrolStartTime != null
        ? item.patrolStartTime
        : '未开始';

    String endTime = item.patrolEndTime != null
        ? item.patrolEndTime
        : '未结束';

    String duration = '';
    if (item.patrolStartTimeStamp != null && item.patrolEndTimeStamp != null &&
        item.patrolStartTimeStamp > 0 && item.patrolEndTimeStamp > 0) {
      int durationInSeconds = (item.patrolEndTimeStamp - item.patrolStartTimeStamp) ~/ 1000;
      duration = TimeHelper.formatDuration(durationInSeconds);
    } else {
      duration = '未完成';
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            controller.goToPatrolRecord();
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.person, size: 18, color: Color(0xFF6884FF)),
                    8.gap,
                    Text(
                      '巡检人员: ${item.name}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                12.gap,
                _buildInfoRow(Icons.access_time, '开始时间: $startTime'),
                8.gap,
                _buildInfoRow(Icons.timer_off, '结束时间: $endTime'),
                8.gap,
                _buildInfoRow(Icons.timelapse, '巡检时长: $duration'),
                12.gap,
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    ElevatedButton(
                      onPressed: () {
                        controller.goToPatrolRecord();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF6884FF),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      child: const Text(
                        '查看详情',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey),
        8.gap,
        Text(
          text,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.black87,
          ),
        ),
      ],
    );
  }
}