import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/global_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/controllers/sqflite_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/app_pages.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/route_helper.dart';

class PhonePatrolRecordController extends GetxController {
  GlobalController globalController = Get.find();
  SQfliteController sqfliteController = Get.find();

  // 数据响应
  RxList historyList = [].obs;

  @override
  void onInit() async {
    super.onInit();
    await findHistoryList();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 获取巡检记录
  findHistoryList() async {
    var historyData = await sqfliteController.findHistory("eid='${globalController.userInfo.value!.data.eid}'");
    historyList.value = historyData.reversed.toList();
    update();
  }

  // 跳转到原始的巡检记录页面
  void goToPatrolRecord() {
    route(Routes.PATROL_RECORD);
  }
}