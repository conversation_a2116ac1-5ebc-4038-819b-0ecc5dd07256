import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/phone_tool_bar.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/patrol/start/patrol_start_controller.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

class PhonePatrolStartView extends StatefulWidget {
  const PhonePatrolStartView({Key? key}) : super(key: key);

  @override
  State<PhonePatrolStartView> createState() => _PhonePatrolStartViewState();
}

class _PhonePatrolStartViewState extends State<PhonePatrolStartView> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this, initialIndex: 1);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PhonePatrolStartController>(
        builder: (controller) {
          return Scaffold(
            backgroundColor: const Color(0xfff2f3f5),
            body: Column(
              children: [
                PhoneToolBar(title: '检测任务(${DateTime.now().toString().substring(0, 10)})'),
                _buildTabBar(),
                Expanded(child: _buildTabBarView(controller)),
              ],
            ),
          );
        }
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: '供配电'),
          Tab(text: '制冷'),
          Tab(text: '智能化'),
        ],
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey,
        labelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
        ),
        indicatorColor: Colors.black,
        indicatorWeight: 2,
        indicatorSize: TabBarIndicatorSize.label,
      ),
    );
  }

  Widget _buildTabBarView(PhonePatrolStartController controller) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildTaskList(controller, '供配电'),
        _buildTaskList(controller, '制冷'),
        _buildTaskList(controller, '智能化'),
      ],
    );
  }

  Widget _buildTaskList(PhonePatrolStartController controller, String category) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildTaskItem('8:00-10:00', '待巡检', Colors.blue, '开始巡检', controller),
        12.gap,
        _buildTaskItem('11:00-13:00', '进行中', Colors.green, '继续巡检', controller, showButtons: true),
        12.gap,
        _buildTaskItem('14:00-16:00', '已完成', Colors.blue, '', controller),
        12.gap,
        _buildTaskItem('14:00-16:00', '已超时', Colors.red, '', controller),
        12.gap,
        _buildTaskItem('8:00-10:00', '待上传', Colors.orange, '上传数据', controller),
      ],
    );
  }

  Widget _buildTaskItem(String timeRange, String status, Color statusColor, String buttonText, PhonePatrolStartController controller, {bool showButtons = false}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                timeRange,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    4.gap,
                    Text(
                      status,
                      style: TextStyle(
                        fontSize: 12,
                        color: statusColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          12.gap,
          Row(
            children: [
              const Icon(Icons.access_time, size: 16, color: Colors.blue),
              4.gap,
              const Text('巡检时长: 2小时30分', style: TextStyle(fontSize: 12, color: Colors.blue)),
              20.gap,
              const Icon(Icons.location_on, size: 16, color: Colors.blue),
              4.gap,
              const Text('测点数: 199', style: TextStyle(fontSize: 12, color: Colors.blue)),
            ],
          ),
          8.gap,
          const Text('房间数量: 199', style: TextStyle(fontSize: 14)),
          const Text('设备数量: 199', style: TextStyle(fontSize: 14)),
          const Text('开始时间: 2024.12.25 12:00:00', style: TextStyle(fontSize: 14)),
          Text(
            status == '待巡检' ? '结束时间: -' : '结束时间: 2024.12.25 12:00:00',
            style: const TextStyle(fontSize: 14),
          ),
          if (buttonText.isNotEmpty || showButtons) ...[
            12.gap,
            if (showButtons)
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => controller.goToPatrolStart(),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.blue),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('继续巡检', style: TextStyle(color: Colors.blue)),
                    ),
                  ),
                  12.gap,
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => controller.goToPatrolStart(),
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: Colors.red),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('结束巡检', style: TextStyle(color: Colors.red)),
                    ),
                  ),
                ],
              )
            else if (buttonText.isNotEmpty)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => controller.goToPatrolStart(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _getButtonColor(status),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(
                    buttonText,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ),
          ],
        ],
      ),
    );
  }

  Color _getButtonColor(String status) {
    switch (status) {
      case '待巡检':
        return Colors.blue;
      case '待上传':
        return Colors.blue;
      default:
        return Colors.blue;
    }
  }
}