import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/number_size_ex.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/common/phone_tool_bar.dart';
import 'package:sjzx_patrol_system_mobile/app/phone/patrol/start/patrol_start_controller.dart';
import 'package:sjzx_patrol_system_mobile/res/assets_res.dart';

class PhonePatrolStartView extends StatelessWidget {
  const PhonePatrolStartView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PhonePatrolStartController>(
        builder: (controller) {
          return Scaffold(
            backgroundColor: const Color(0xfff2f3f5),
            body: Column(
              children: [
                const PhoneToolBar(title: '日常巡检'),
                20.gap,
                _buildContent(controller),
              ],
            ),
          );
        }
    );
  }

  Widget _buildContent(PhonePatrolStartController controller) {
    return Expanded(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoCard(),
            20.gap,
            _buildStartButton(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '日常巡检说明',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          10.gap,
          const Text(
            '日常巡检是对设备进行定期检查的过程，确保设备正常运行。巡检过程中需要检查设备的运行状态、外观、温度等参数，并记录异常情况。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
          15.gap,
          const Text(
            '巡检步骤：',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          10.gap,
          _buildStepItem('1. 开始巡检：点击下方"开始巡检"按钮'),
          _buildStepItem('2. 选择房间：根据巡检计划选择需要巡检的房间'),
          _buildStepItem('3. 检查设备：对房间内的设备进行检查'),
          _buildStepItem('4. 记录异常：如发现异常，拍照并记录'),
          _buildStepItem('5. 完成巡检：完成所有设备检查后结束巡检'),
        ],
      ),
    );
  }

  Widget _buildStepItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.check_circle, size: 16, color: Colors.blue),
          8.gap,
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStartButton(PhonePatrolStartController controller) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ElevatedButton(
        onPressed: () {
          controller.goToPatrolStart();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF6884FF),
          minimumSize: const Size(double.infinity, 50),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          '开始巡检',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}