import 'package:get/get.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/app_pages.dart';
import 'package:sjzx_patrol_system_mobile/app/routes/route_helper.dart';

class PhonePatrolStartController extends GetxController {

  @override
  void onInit() {
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  // 跳转到原始的巡检开始页面
  void goToPatrolStart() {
    route(Routes.PATROL_START);
  }
}