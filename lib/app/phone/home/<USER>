import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:open_filex/open_filex.dart';
import 'package:sjzx_patrol_system_mobile/app/ext/toast.dart';
import 'package:sjzx_patrol_system_mobile/app/modules/home/<USER>/home_controller.dart';
import 'package:sjzx_patrol_system_mobile/app/retrofit/entity/phone_version_detail_resp.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/loading_helper.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/logger.dart';
import 'package:sjzx_patrol_system_mobile/app/utils/string_util.dart';

import '../../../main.dart';
import '../../api/env_config.dart';
import '../../retrofit/ir_datasource.dart';
import '../../utils/screenutil.dart';

class PhoneHomeController extends HomeController {

  RxInt showSetting = 0.obs;
  Rx<VersionResp> showUpgrade = VersionResp(id: '',number: '', name: '',
      apkName: '',createTime: 0, description: '', url: '', updateTime: 0,delStatus:0).obs;

  @override
  logout() {
    showSetting.value ++;
  }

  @override
  checkAppUpgrade({bool? showToast}) async {
    version.value = await getAppVersion();
    logger('当前应用的版本号： ${version.value}');
    var userDataSource = IrDataSource(retrofitDio , baseUrl: Host.userApi);
    var remoteVersion = await userDataSource.getVersion();
    if(remoteVersion.success()){
      if(remoteVersion.data != null){
        var r = compareVersions(remoteVersion.data!.number, version.value);
        if(r > 0){
          showUpgrade.value = remoteVersion.data!;
        }else {
          if(showToast == true) toast('没有新版本');
        }
      }

    }else {
      if(showToast == true) toast(remoteVersion.msg);
    }
  }

  int compareVersions(String? remote, String? local) {
    try {
      if(StringUtil.isEmpty(remote) || StringUtil.isEmpty(local)){
        return 0;
      }
      List<String> parts1 = remote!.split('.');
      List<String> parts2 = local!.split('.');

      for (int i = 0; i < parts1.length || i < parts2.length; i++) {
        int part1 = i < parts1.length ? int.parse(parts1[i]) : 0;
        int part2 = i < parts2.length ? int.parse(parts2[i]) : 0;

        if (part1 != part2) {
          return part1 - part2;
        }
      }
      return 0; // 版本号完全相同
    }catch(e){
      return 0;
    }
  }

  downApp(VersionResp versionResp) async {
    var flag = await checkPermission();
    var localPath = await getAppPath();
    if (flag) {
      String appName = versionResp.apkName ?? '';
      String savePath = "$localPath/$appName";
      String appUrl = versionResp.url ?? '';
      // String appUrl = 'http://*********:9002/rangeidc-office-doc/mobile_release_1.0.1.apk?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=joinu%2F20240611%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20240611T053924Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=d54a34d17fac78a70c6d14e47445c00748260190dcb41bb465fda562d7b20d18';
      Dio dio = Dio();
      LoadingManager.showLoading(msg: '正在下载');
      try {
        await dio.download(appUrl, savePath,
            onReceiveProgress: (received, total) {
              if (total != -1) {
                ///当前下载的百分比例
                logger((received / total * 100).toStringAsFixed(0) + "%");
              }
            });

        LoadingManager.disMiss();
        await OpenFilex.open(savePath,
            type: "application/vnd.android.package-archive");
      } catch (e) {
        LoadingManager.disMiss();
        toast(e);
      }
    }
  }

}