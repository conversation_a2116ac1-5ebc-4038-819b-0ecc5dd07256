// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LoginRespData _$LoginRespDataFromJson(Map<String, dynamic> json) =>
    LoginRespData(
      eid: json['eid'] as String?,
      name: json['name'] as String,
      phone: json['phone'] as String,
      passWorld: json['passWorld'] as String?,
      deptName: json['deptName'] as String?,
      technicalPost: json['technicalPost'] as String?,
      deptPost: json['deptPost'] as String?,
      gender: (json['gender'] as num?)?.toInt(),
      occupation: json['occupation'] as String?,
      roomTypeName: json['roomTypeName'] as String?,
      createTime: (json['createTime'] as num?)?.toInt(),
      floor: json['floor'] as String?,
      shifts: json['shifts'] as String?,
      roomType: json['roomType'] as String?,
      roomTypeInfoList: (json['roomTypeInfoList'] as List<dynamic>?)
          ?.map((e) => RoomTypeInfoList.fromJson(e as Map<String, dynamic>))
          .toList(),
      number: json['number'] as String?,
      versionName: json['versionName'] as String?,
      apkName: json['apkName'] as String?,
      versionCreateTime: (json['versionCreateTime'] as num?)?.toInt(),
      description: json['description'] as String?,
      url: json['url'] as String?,
      livingPlace: json['livingPlace'] as String?,
      companyName: json['companyName'] as String?,
      companyId: json['companyId'] as String?,
      deviceMaintainPer: json['deviceMaintainPer'] as String?,
    );

Map<String, dynamic> _$LoginRespDataToJson(LoginRespData instance) =>
    <String, dynamic>{
      'eid': instance.eid,
      'name': instance.name,
      'phone': instance.phone,
      'passWorld': instance.passWorld,
      'deptName': instance.deptName,
      'technicalPost': instance.technicalPost,
      'deptPost': instance.deptPost,
      'gender': instance.gender,
      'occupation': instance.occupation,
      'roomTypeName': instance.roomTypeName,
      'createTime': instance.createTime,
      'floor': instance.floor,
      'shifts': instance.shifts,
      'roomType': instance.roomType,
      'livingPlace': instance.livingPlace,
      'number': instance.number,
      'versionName': instance.versionName,
      'apkName': instance.apkName,
      'versionCreateTime': instance.versionCreateTime,
      'description': instance.description,
      'url': instance.url,
      'roomTypeInfoList': instance.roomTypeInfoList,
      'companyName': instance.companyName,
      'companyId': instance.companyId,
      'deviceMaintainPer': instance.deviceMaintainPer,
    };
