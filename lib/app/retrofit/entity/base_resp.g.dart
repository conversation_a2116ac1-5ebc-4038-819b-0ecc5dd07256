// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_resp.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseResp<T> _$BaseRespFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    BaseResp<T>(
      (json['code'] as num).toInt(),
      json['msg'] as String,
      fromJsonT(json['data']),
    );

Map<String, dynamic> _$BaseRespToJson<T>(
  BaseResp<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'code': instance.code,
      'msg': instance.msg,
      'data': toJsonT(instance.data),
    };
