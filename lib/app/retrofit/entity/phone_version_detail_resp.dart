

/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class VersionResp {
  String? id;
  String? number;
  String? name;
  String? apkName;
  int? createTime;
  String? description;
  String? url;
  int? updateTime;
  int? delStatus;

  VersionResp({this.id, this.number, this.name, this.apkName, this.createTime, this.description, this.url, this.updateTime, this.delStatus});

  VersionResp.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    number = json['number'];
    name = json['name'];
    apkName = json['apkName'];
    createTime = json['createTime'];
    description = json['description'];
    url = json['url'];
    updateTime = json['updateTime'];
    delStatus = json['delStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['number'] = number;
    data['name'] = name;
    data['apkName'] = apkName;
    data['createTime'] = createTime;
    data['description'] = description;
    data['url'] = url;
    data['updateTime'] = updateTime;
    data['delStatus'] = delStatus;
    return data;
  }
}
