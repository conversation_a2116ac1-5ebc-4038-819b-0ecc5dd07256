
/*
// Example Usage
Map<String, dynamic> map = jsonDecode(<myJSONString>);
var myRootNode = Root.fromJson(map);
*/
class IrTaskRespItem {
  String? id;
  String? inspector;
  String? inspectorId;
  String? deptId;
  String? deptName;
  int? type;
  int? deviceCount;
  int? status;
  List<InfraredInspectionDeviceTypeVO?>? infraredInspectionDeviceTypePadVOS;

  IrTaskRespItem({this.id, this.inspector, this.inspectorId, this.deptId, this.deptName, this.type, this.deviceCount, this.status, this.infraredInspectionDeviceTypePadVOS});

  IrTaskRespItem.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    inspector = json['inspector'];
    inspectorId = json['inspectorId'];
    deptId = json['deptId'];
    deptName = json['deptName'];
    type = json['type'];
    deviceCount = json['deviceCount'];
    status = json['status'];
    if (json['infraredInspectionDeviceTypePadVOS'] != null) {
      infraredInspectionDeviceTypePadVOS = <InfraredInspectionDeviceTypeVO>[];
      json['infraredInspectionDeviceTypePadVOS'].forEach((v) {
        infraredInspectionDeviceTypePadVOS!.add(InfraredInspectionDeviceTypeVO.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['inspector'] = inspector;
    data['inspectorId'] = inspectorId;
    data['deptId'] = deptId;
    data['deptName'] = deptName;
    data['type'] = type;
    data['deviceCount'] = deviceCount;
    data['status'] = status;
    data['infraredInspectionDeviceTypePadVOS'] =infraredInspectionDeviceTypePadVOS != null ? infraredInspectionDeviceTypePadVOS!.map((v) => v?.toJson()).toList() : null;
    return data;
  }



}


class InfraredInspectionDeviceTypeVO {
  String? deviceTypeId;
  String? name;
  List<InspectionDevice?>? inspectionDevices;

  InfraredInspectionDeviceTypeVO({this.deviceTypeId, this.name, this.inspectionDevices});

  InfraredInspectionDeviceTypeVO.fromJson(Map<String, dynamic> json) {
    deviceTypeId = json['deviceTypeId'];
    name = json['name'];
    if (json['inspectionDevices'] != null) {
      inspectionDevices = <InspectionDevice>[];
      json['inspectionDevices'].forEach((v) {
        inspectionDevices!.add(InspectionDevice.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['deviceTypeId'] = deviceTypeId;
    data['name'] = name;
    data['inspectionDevices'] =inspectionDevices != null ? inspectionDevices!.map((v) => v?.toJson()).toList() : null;
    return data;
  }
}

// 设备
class InspectionDevice {
  String? id;
  String? deptId;
  String? name;
  String? infraredDeviceTypeId;
  String? code;
  String? num;
  String? installationSite;
  String? project;
  String? upperLimitValue;
  int? delStatus;
  int? createTime;

  InspectionDevice({this.id, this.deptId, this.name, this.infraredDeviceTypeId, this.code, this.num, this.installationSite, this.project, this.upperLimitValue
    , this.delStatus, this.createTime});

  InspectionDevice.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    deptId = json['deptId'];
    name = json['name'];
    infraredDeviceTypeId = json['infraredDeviceTypeId'];
    code = json['code'];
    num = json['num'];
    installationSite = json['installationSite'];
    project = json['project'];
    upperLimitValue = json['upperLimitValue'];
    delStatus = json['delStatus'];
    createTime = json['createTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['id'] = id;
    data['deptId'] = deptId;
    data['name'] = name;
    data['infraredDeviceTypeId'] = infraredDeviceTypeId;
    data['code'] = code;
    data['num'] = num;
    data['installationSite'] = installationSite;
    data['project'] = project;
    data['upperLimitValue'] = upperLimitValue;
    data['delStatus'] = delStatus;
    data['createTime'] = createTime;
    return data;
  }
}






