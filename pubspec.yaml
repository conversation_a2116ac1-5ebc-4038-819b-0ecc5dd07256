name: sjzx_patrol_system_mobile
version: 1.0.1
publish_to: none
description: A new Flutter project.
environment: 
  sdk: '>=2.19.0 <3.0.0'

dependencies: 
  cupertino_icons: ^1.0.2
  percent_indicator: ^4.0.1
  flutter_screenutil: ^5.6.1
  dio: ^5.0.1
  fluttertoast: ^8.0.9
#  flutter_easy_permission: ^1.1.2
  flutter_scankit: ^2.0.4
  signature: ^5.3.2
#  wifi_info_plugin_plus: ^2.0.2
  shared_preferences: ^2.1.0
  sqflite: ^2.2.6
  path: any
  collection: ^1.17.0
  connectivity_plus: ^6.1.1
  network_info_plus: ^6.1.2
  geolocator: ^9.0.2
  flutter_spinkit: ^5.1.0
  qr_flutter: ^4.0.0
  intl: ^0.19.0
  package_info_plus: ^4.2.0
  path_provider: ^2.0.15
  open_filex: ^4.6.0
  permission_handler: ^10.2.0
  get: 4.6.6
  get_storage: ^2.0.3
  flutter: 
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  image_picker: ^1.0.1
  gap: 3.0.1
  floor: ^1.3.0 #
  isolate: ^2.0.0
  #  retrofit
  retrofit: '>=4.0.0 <5.0.0'
  logger: ^1.1.0   #for logging purpose
  json_annotation: ^4.8.1
  flutter_easyloading: ^3.0.5 # 更好的控制loading
  flutter_image_compress: 2.1.0
  #  retrofit
  device_info_plus: ^9.1.2

  extended_image_library: 3.6.0
  uuid: ^4.2.2

  change_app_package_name: ^1.4.0

#  ir plugin
  flutter_ir_plugin:
    path: .././flutter_ir_plugin

#  flutter_ir_plugin:
#    git:
#      url: ****************:liangjt/flutter_ir_plugin.git
#      ref: dev

dev_dependencies: 
  flutter_lints: ^2.0.0
  floor_generator: ^1.3.0
  build_runner: '>2.1.1'
  json_serializable: '>4.4.0'

  retrofit_generator: '>=7.0.0 <8.0.0'

  flutter_test: 
    sdk: flutter

flutter: 
  uses-material-design: true

  fonts:
    - family: iconfont
      fonts:
        - asset: assets/fonts/iconfont.ttf

  assets:
    - assets/images/backImage.jpg
    - assets/images/drop.png
    - assets/images/icon/Power.png
    - assets/images/icon/back.png
    - assets/images/icon/bumen.png
    - assets/images/icon/delete.png
    - assets/images/icon/department_blue.png
    - assets/images/icon/dian.png
    - assets/images/icon/left.png
    - assets/images/icon/next.png
    - assets/images/icon/position_blue.png
    - assets/images/icon/room_title.png
    - assets/images/icon/set.png
    - assets/images/icon/speciality_blue.png
    - assets/images/icon/time.png
    - assets/images/icon/zhiwei.png
    - assets/images/icon/zhuanye.png
    - assets/images/ir_last_device.png
    - assets/images/ir_next_device.png
    - assets/images/ir_start.png
    - assets/images/logo.png
    - assets/images/over.png
    - assets/images/patrolA.png
    - assets/images/patrolB.png
    - assets/images/patrolC.png
    - assets/images/patrolD.png
    - assets/images/phone_check_bg.png
    - assets/images/phone_device_type.png
    - assets/images/phone_login_bg.png
    - assets/images/phone_mobile_icon.png
    - assets/images/phone_pwd_icon.png
    - assets/images/phone_record_bg.png
    - assets/images/phone_setting_icon.png
    - assets/images/phone_todo_hot_pic.png
    - assets/images/phone_tool_back.png
    - assets/images/phone_update_data_icon.png
    - assets/images/searchCode_bg.png
    - assets/images/singleA.png
    - assets/images/singleB.png
    - assets/images/singleC.png
    - assets/images/update.png
    - assets/images/userLogo.png


